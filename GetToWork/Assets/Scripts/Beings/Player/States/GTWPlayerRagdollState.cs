using Isto.Core.Inputs;
using Isto.GTW.Inputs;
using Isto.Core.StateMachine;
using UnityEngine;
using Zenject;
using Isto.Core.Achievements;
using Isto.Core.UI;

namespace Isto.GTW.Player.States
{
    [CreateAssetMenu(fileName = "GTW-PlayerRagdollState", menuName = "Scriptables/GTW/States/New GTW Player Ragdoll State")]
    public class GTWPlayerRagdollState : State
    {
        public float resetTime = 1f;

        public float giveUpCooldownTime = 1f;

        public bool AutoReset
        {
            get => _autoReset;
            set => _autoReset = value;
        }

        public bool InGiveUpMode
        {
            get => _giveUpMode;
            set => _giveUpMode = value;
        }

        public bool AllowGrab
        {
            get => _allowGrab;
            set => _allowGrab = value;
        }

        public bool ResetOnInput
        {
            get => _resetOnInput;
            set => _resetOnInput = value;
        }

        public bool AllowRagdollRecovery
        {
            get => _allowRagdollRecovery;
            set => _allowRagdollRecovery = value;
        }


        private PlayerController _playerController;
        private bool _allowGrab = true;
        private bool _autoReset = true;
        private bool _giveUpMode = false;
        private bool _allowRagdollRecovery = true;
        private bool _resetOnInput = false;
        private float _ragdollTime = 0f;
        private float _totalGiveUpTime = 0f;
        private bool _giveUpAchievementSent = false;

        private bool _isEnterFrame = false;
        private bool _interuptedFromRespawn = false;


        // inject

        private IAchievements _achievements;

        [Inject]
        public void Inject(IAchievements achievements)
        {
            _achievements = achievements;
        }


        // lifecycle

        public override void Enter(ScriptableStateMachine controller)
        {
            Debug.Log("Activating Player Ragdoll");

            _playerController = controller as PlayerController;

            _playerController.PlayerIkController.PlayerRagdoll.SetRagdollActive(true);
            _playerController.SetPhysicsActive(false);
            ResetRagdollTime();

            // _torquePlayerController.ResetVelocity();
            _playerController.SetVelocity(Vector3.zero, Vector3.zero);
            // _torquePlayerController.Rigidbody.velocity = Vector3.zero;
            // _torquePlayerController.Rigidbody.angularVelocity = Vector3.zero;
            _playerController.PlayerCollisionHandler.ClearGroundHit();

            _isEnterFrame = true;
        }

        public override IState Run(ScriptableStateMachine controller)
        {
            if (_playerController.CheckpointManager != null)
            {
                if (_playerController.restartAbilityAvailable && _playerController.InputControls.GetButtonDown(GTWUserActions.RESPAWNACTIVECHECKPOINT))
                {
                    _interuptedFromRespawn = true;
                    _playerController.CheckpointManager.Respawn(showBlackOut: true);

                    return _playerController._moveState;
                }

                if (_playerController.restartStartingCheckpointAbilityAvailable && _playerController.InputControls.GetButtonDown(GTWUserActions.RESPAWNSTARTINGCHECKPOINT))
                {
                    _interuptedFromRespawn = true;
                    _playerController.CheckpointManager.RespawnAtStartingCheckpoint();

                    return _playerController._moveState;
                }
            }

            if (_playerController.noClipAbilityAvailable && _playerController.InputControls.GetButtonDown(GTWUserActions.NOCLIP))
            {
                return _playerController._noClipState;
            }

            _ragdollTime += Time.deltaTime;

            if ((AutoReset && _ragdollTime > resetTime) ||
                (_playerController.ragDollAbilityAvailable &&
                 _allowRagdollRecovery &&
                 _playerController.InputControls.GetButtonDown(GTWUserActions.GIVEUP) &&
                 _ragdollTime > giveUpCooldownTime))
            {
                return _playerController._moveState;
            }

            if(!_giveUpAchievementSent && InGiveUpMode)
            {
                _totalGiveUpTime += Time.deltaTime;
                if (_totalGiveUpTime >= IGTWAchievements.REAL_QUITTER_TIME)
                {
                    _achievements.TriggerAchievement(IGTWAchievements.GTWAchievementsEnum.REAL_QUITTER);
                    // it's OK to resent an achievement (does nothing) but I doubt it's OK to spam it even then
                    _giveUpAchievementSent = true;
                }
            }

            if (_resetOnInput && _playerController.MovementInput.sqrMagnitude > 0.01f)
            {
                return _playerController._moveState;
            }

            if (!_isEnterFrame && //Activating/deactivating ragdoll on the same frame bugs
                AllowGrab &&
                !AutoReset &&
                _playerController.grabAbilityAvailable &&
                !_playerController.IsGrabLinkActive &&
                _playerController.InputControls.GetButton(GTWUserActions.GRAB) &&
                _ragdollTime > giveUpCooldownTime)
            {
                _playerController.ActivateGrabLink();
                return _playerController._moveState;
            }

            _playerController.UpdateBodyVelocity();

            _isEnterFrame = false;

            return this;
        }

        public override void Exit(ScriptableStateMachine controller)
        {
            Rigidbody pelvisRigidBody = _playerController.PlayerIkController.PlayerRagdoll.PelvisRigidBody;
            Vector3 velocity = pelvisRigidBody.linearVelocity;
            Vector3 angularVelocity = pelvisRigidBody.angularVelocity;

            _playerController.PlayerCollisionHandler.ClearGroundHit();

            _playerController.SetPhysicsActive(true);

            if (_interuptedFromRespawn)
            {
                _playerController.ResetVelocityToZero();
                _playerController.PlayerIkController.PlayerRagdoll.SetRagdollActive(false, true);
            }
            else
            {
                Transform root = pelvisRigidBody.transform;
                _playerController.TeleportCharacter(root.position + -_playerController.GravityDirection * 0.01f);
                if (!_playerController.TeleportToGround(_playerController.BallRadius + 0.02f))
                {
                    _playerController.TeleportCharacter(root.position);
                }
            
                _playerController.SetVelocity(velocity, angularVelocity);
                
                _playerController.PlayerIkController.PlayerRagdoll.SetRagdollActive(false);
            }

            AutoReset = true;
            AllowGrab = true;
            _resetOnInput = false;
            InGiveUpMode = false;
            _interuptedFromRespawn = false;
        }

        public void ResetRagdollTime()
        {
            _ragdollTime = 0f;
        }
    }
}