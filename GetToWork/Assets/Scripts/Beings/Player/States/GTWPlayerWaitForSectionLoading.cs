using Isto.Core.StateMachine;
using Isto.GTW.SectionLoading;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Isto.GTW.Player.States
{
    /// <summary>
    /// Freeze the player and wait for sections to load/unload to avoid falling through the floor after a teleport.
    /// </summary>
    [CreateAssetMenu(fileName = "GTW-PlayerWaitForSectionLoading", menuName = "Scriptables/GTW/States/New GTW Player WaitForSectionLoading")]
    public class GTWPlayerWaitForSectionLoading : State
    {
        public bool teleportToGroundOnExit = false;
        public bool keepVelocity = false;
        public Vector3 velocity = Vector3.zero;
        public Vector3 angularVelocity = Vector3.zero;
        
        private PlayerController _playerController;

        public override void Enter(ScriptableStateMachine controller)
        {
            Debug.Log("Activating Wait for Section Loading");
            
            _playerController = controller as PlayerController;
            
            _playerController.Rigidbody.isKinematic = true;
        }

        public override IState Run(ScriptableStateMachine controller)
        {
            if (!SectionLoader.IsBusy)
            {
                return _playerController._moveState;
            }
            
#if UNITY_EDITOR
            if (EditorPrefs.GetBool(SectionLoader.LOADING_DISABLED_EDITOR_PREF_KEY, false))
            {
                return _playerController._moveState;
            }
            else
            {
                Debug.Log("Waiting for Section Loading");
            }
#endif

            return this;
        }

        public override void Exit(ScriptableStateMachine controller)
        {
            _playerController.Rigidbody.isKinematic = false;

            if (teleportToGroundOnExit)
            {
                _playerController.TeleportToGround();
                teleportToGroundOnExit = false;
            }

            if (keepVelocity)
            {
                _playerController.SetVelocity(velocity, angularVelocity);
                keepVelocity = false;
            }
        }
    }
}