using UnityEngine;
using Zenject;

namespace Isto.GTW.Player
{
    public class PlayerAbilityDependant : MonoBehaviour
    {
        // UNITY HOOKUPS
        
        [SerializeField] private GameObject _ragdollButtonPrompt = null;
        [SerializeField] private GameObject _restartButtonPrompt = null;


        // INJECTION

        PlayerController _player;

        [Inject]
        public void Inject(PlayerController player)
        {
            _player = player;
        }
        
        
        // UNITY LIFECYCLE

        private void Update()
        {
            UpdatePrompt(_ragdollButtonPrompt, _player.ragDollAbilityAvailable);
            UpdatePrompt(_restartButtonPrompt, _player.restartAbilityAvailable);
        }
        
        
        // OTHER METHODS

        private void UpdatePrompt(GameObject prompt, bool isActive)
        {
            if (prompt == null)
            {
                return;
            }

            if (prompt.activeSelf != isActive)
            {
                prompt.SetActive(isActive);
            }
        }
    }
}