// Copyright Isto Inc.

using Isto.Core;
using Isto.Core.Game;
using Isto.GTW.Configuration;
using Isto.GTW.Player;
using Isto.GTW.Providers;
using UnityEngine;
using Zenject;

namespace Isto.GTW.Managers
{
    public class GTWDoinklerLevelTimeManager : MonoBehaviour
    {
        // INJECTION

        private GTWProgressProvider _gameProgressProvider;
        private GTWGameState _gameState;
        private PlayerController _playerController;
        private IDoinklerWorldDefinitionProvider _doinklerWorldDefinitionProvider;

        [Inject]
        public void Inject(IGameProgressProvider gameProgressProvider, GTWGameState gameState,
            PlayerController playerController, IDoinklerWorldDefinitionProvider doinklerWorldDefinitionProvider)
        {
            _gameProgressProvider = gameProgressProvider as GTWProgressProvider;
            _gameState = gameState;
            _playerController = playerController;
            _doinklerWorldDefinitionProvider = doinklerWorldDefinitionProvider;
        }


        // LIFECYCLE EVENTS

        private void OnEnable()
        {
            RegisterEvents();
        }

        private void OnDisable()
        {
            UnregisterEvents();
        }


        // EVENT HANDLING

        private void RegisterEvents()
        {
            Events.SubscribeWithParams(GTWEvents.DOINKLER_STAGE_RESET, Events_OnDoinklerStageReset);
            Events.SubscribeWithParams(GTWEvents.DOINKLER_FULL_STAGE_RESET, Events_OnDoinklerFullStageReset);
        }

        private void Events_OnDoinklerStageReset(object[] args)
        {
            bool saveData = (bool)args[0];

            _playerController.CheckpointManager.Respawn(showBlackOut: true);
            _playerController.TeleportToGround();
            int currentLevel = _gameProgressProvider.GetCurrentGameProgressLevel();
            if (saveData)
            {
                float elapsedTimeInLevel = _gameProgressProvider.GetGameSecondsElapsedInLevel(currentLevel);
                // Reset the current level timer
                _gameProgressProvider.GameTime[currentLevel] = 0f;

                // Get the level definition, falling back to default if needed
                GTWGameLevelDefinition levelDefinition = _gameState.GameLevelDefinition ??
                                                         _doinklerWorldDefinitionProvider.Current.GameLevels[currentLevel];

                // Update total time spent in this level
                float totalTimeInLevel = PlayerPrefs.GetFloat(levelDefinition.UniqueIDTotal);
                PlayerPrefs.SetFloat(levelDefinition.UniqueIDTotal, totalTimeInLevel + elapsedTimeInLevel);
            }
            else
            {
                // Reset the current level timer
                _gameProgressProvider.GameTime[currentLevel] = 0f;
            }

            // Trigger stage completion and start events
            Events.RaiseEvent(GTWEvents.DOINKLER_STAGE_COMPLETE);
            Events.RaiseEvent(GTWEvents.DOINKLER_STAGE_START);

        }
        
        private void Events_OnDoinklerFullStageReset(object[] args)
        {
            bool saveData = (bool)args[0];

            _playerController.CheckpointManager.Respawn(showBlackOut: true);
            _playerController.TeleportToGround();
            int currentLevel = _gameProgressProvider.GetCurrentGameProgressLevel();
            if (saveData)
            {
                // Reset all level timers
                _gameProgressProvider.ResetLevelTimes();

                // Get the level definition, falling back to default if needed
                GTWGameLevelDefinition levelDefinition = _gameState.GameLevelDefinition ??
                                                         _doinklerWorldDefinitionProvider.Current.GameLevels[currentLevel];

                // Update total time spent in this level
                PlayerPrefs.SetFloat(levelDefinition.UniqueIDTotal, 0f);
            }
            else
            {
                // Reset the current level timer
                _gameProgressProvider.ResetLevelTimes();
            }

            // Trigger stage completion and start events
            Events.RaiseEvent(GTWEvents.DOINKLER_STAGE_COMPLETE);
            Events.RaiseEvent(GTWEvents.DOINKLER_STAGE_START);

        }

        private void UnregisterEvents()
        {
            Events.UnSubscribeWithParams(GTWEvents.DOINKLER_STAGE_RESET, Events_OnDoinklerStageReset);
            Events.UnSubscribeWithParams(GTWEvents.DOINKLER_FULL_STAGE_RESET, Events_OnDoinklerFullStageReset);
        }
    }
}