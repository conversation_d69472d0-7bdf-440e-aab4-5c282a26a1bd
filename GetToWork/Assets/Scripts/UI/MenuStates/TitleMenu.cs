// Copyright Isto Inc.

using Isto.Core.Audio;
using Isto.Core.StateMachine;
using Isto.Core.UI;
using Isto.GTW.UI;
using UnityEngine;
using UnityEngine.UI;
using Zenject;

namespace Isto.GTW
{
    /// <summary>
    /// The title menu for GTW that allows for additional functionality within the screen
    /// </summary>
    public class TitleMenu : UISimpleTitleMenuState
    {
        // unity hookup

        [Header("Doinkler Special")]
        public UIDoinklerSpecial doinklerSpecialSubState;

        [Header("Extra URLs")]
        public string howWeMadeUrl;
        public string speedrunUrl;

        [Header("GTW Menu Buttons")]
        [SerializeField] private Selectable _doinklerPortfolioButton;
        [SerializeField] private Selectable _doinklerSpecialButton;
        [SerializeField] private Selectable _howWeMadeTheGameButton;


        // INJECTION

        private IGameMusic _music;

        [Inject]
        private void Inject(IGameMusic music)
        {
            _music = music;
        }


        // LIFECYCLE EVENTS

        private void OnDisable()
        {
            _music.StopMusic();
        }

        public override void Enter(MonoStateMachine controller)
        {
            base.Enter(controller);

            _music.PlayMusic(MusicTrack.Title);
        }


        // EVENTS

        public void DoinklerPortfolioButton_OnButtonClicked()
        {
            _previousSelection = _doinklerPortfolioButton;
            CloseAllStatesAndEnterNewState(gameModeSubstate);
        }

        public void DoinklerSpecialButton_OnButtonClicked()
        {
            _previousSelection = _doinklerSpecialButton;
            CloseAllStatesAndEnterNewState(doinklerSpecialSubState);
        }

        public void WatchHowWeMadeButton_OnButtonClicked()
        {
            _previousSelection = _howWeMadeTheGameButton;
            Application.OpenURL(howWeMadeUrl);
        }

        public void SpeedrunCompetitionButton_OnButtonClicked()
        {
            Application.OpenURL(speedrunUrl);
        }


        // OTHER METHODS

        protected override void SetupLoadButton()
        {
            // We only have one save slot for the main game
            // so we just check that one
            SetLoadButton(interactable: false);
            int slot = (int)GTWGameState.SaveSlotId.MAIN_GAME;
            _gameData.HasSaveData(slot, (dataExists) =>
            {
                SetLoadButton(interactable: dataExists);
            });
        }
    }
}