// Copyright Isto Inc.

using Isto.Core;
using System;
using Isto.Core.Audio;
using Isto.Core.Game;
using Isto.Core.Inputs;
using Isto.Core.StateMachine;
using Isto.Core.UI;
using Isto.GTW.Configuration;
using Isto.GTW.LevelFeatures;
using Isto.GTW.Managers;
using Isto.GTW.Player;
using Isto.GTW.Tools;
using Isto.GTW.Providers;
using PixelCrushers.DialogueSystem;
using TMPro;
using UnityEngine;
using System.Linq;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Zenject;

namespace Isto.GTW.UI
{
    /// <summary>
    /// Manages the popup that appears after completing a level, showing stats and providing navigation options.
    /// </summary>
    public class GTWUINextLevelPopup : MonoState
    {
        // UNITY HOOKUP

        [Header("Canvas")]
        [SerializeField] private CanvasGroup _canvasGroup;

        [Header("Unity Hookup")]
        [SerializeField] private TextMeshProUGUI _panelHeaderLabel;
        [SerializeField] private TextMeshProUGUI _levelTitleLabel;
        [SerializeField] private Image _selectedLevelImage;
        [SerializeField] private TextMeshProUGUI _timeLabel;
        [SerializeField] private TextMeshProUGUI _totalTimeLabel;
        [SerializeField] private TextMeshProUGUI _bestTimeLabel;
        [SerializeField] private GameObject _newBestTimeLabel;
        [SerializeField] private Image _progressCircleCurrentFile;
        [SerializeField] private TextMeshProUGUI _progressLabelCurrentFile;
        [SerializeField] private Image _progressCircleAudioFilesHeard;
        [SerializeField] private TextMeshProUGUI _progressLabelAudioFilesHeard;
        [SerializeField] private Image _progressCircleTotalProgress;
        [SerializeField] private TextMeshProUGUI _progressLabelTotalProgress;

        [Header("Buttons")]
        [SerializeField] private CoreButtonController _nextLevelButton;
        [SerializeField] private CoreButtonController _retryLevelButton;
        [SerializeField] private CoreButtonController _levelSelectButton;
        [SerializeField] private CoreButtonController _quitToMainButton;

        [Header("Doinkler Data Hookup")]
        [SerializeField] private UISetGameModeSubState _doinklerLevelSelectSubState;
        [SerializeField] private DialogueDatabase _doinklerDialogueDatabase;
        [SerializeField] private GTWDoinklerPortfolioDefinition _doinklerPortfolioDefinition;


        // OTHER FIELDS

        private GameObject _previousSelection;
        private GTWGameLevelDefinition _currentLevel;
        private GTWGameLevelDefinition _nextLevel;
        private MonoPushdownStateMachine _menuController;
        private bool _isOpen;


        // PROPERTIES

        public bool IsOpen => _isOpen;


        // EVENTS

        private Action _onRespawn;


        // INJECTION

        private IControls _controls;
        private UISounds _uiSounds;
        private GTWGameState _gameState;
        private CheckpointManager _checkpointManager;
        private PlayerController _playerController;
        private GTWProgressProvider _gameProgressProvider;
        private LevelVariables _levelVariables;
        private IDoinklerWorldDefinitionProvider _doinklerWorldDefinitionProvider;


        [Inject]
        public void Inject(IControls controls, UISounds uiSounds, GTWGameState gameState,
            CheckpointManager checkpointManager, PlayerController playerController, IGameProgressProvider gameProgressProvider,
            LevelVariables levelVariables, IDoinklerWorldDefinitionProvider doinklerWorldDefinitionProvider)
        {
            _controls = controls;
            _uiSounds = uiSounds;
            _gameState = gameState;
            _checkpointManager = checkpointManager;
            _playerController = playerController;
            _gameProgressProvider = gameProgressProvider as GTWProgressProvider;
            _levelVariables = levelVariables;
            _doinklerWorldDefinitionProvider = doinklerWorldDefinitionProvider;
        }


        // LIFECYCLE EVENTS

        private void Awake()
        {
            ClosePopup();
        }

        public override void Enter(MonoStateMachine controller)
        {
            _menuController = controller as MonoPushdownStateMachine;
            _previousSelection = Controls.UsingController ? EventSystem.current.currentSelectedGameObject : null;

            // Get level data
            int currentLevel = _gameProgressProvider.GetCurrentGameProgressLevel();
            float currentLevelTime = _gameProgressProvider.GetGameSecondsElapsedInLevel(currentLevel);

            // Handle checkpoint setup
            if (_nextLevel && _nextLevel.CheckpointNumber > currentLevel)
            {
                _checkpointManager.SetActiveCheckpoint(_checkpointManager.CheckpointData.levelCheckpointDatas[_nextLevel.CheckpointNumber]);
                _checkpointManager.SetActiveCheckpoint(_checkpointManager.CheckpointData.levelCheckpointDatas[_currentLevel.CheckpointNumber]);
            }

            // Progress of completed levels in file
            float currentFileProgress = GetCompletedLevelsInFilePercentage(_doinklerWorldDefinitionProvider.Current);
            _progressCircleCurrentFile.fillAmount = currentFileProgress;
            _progressLabelCurrentFile.text = (currentFileProgress).ToString("P0");

            // Progress of Audio Files
            float currentFileAudioProgress = GetActivatedDoinklerDialoguesPercentage();
            _progressCircleAudioFilesHeard.fillAmount = currentFileAudioProgress;
            _progressLabelAudioFilesHeard.text = (currentFileAudioProgress).ToString("P0");

            // Progress of completed levels in Doinkler Portfolio
            float totalDoinklerFileProgress = GetCompletedLevelsInDoinklerPercentage();
            _progressCircleTotalProgress.fillAmount = totalDoinklerFileProgress;
            _progressLabelTotalProgress.text = (totalDoinklerFileProgress).ToString("P0");

            // Update UI elements with level data
            _timeLabel.SetText(GTWUtils.GetFormattedTimeOrDefaultString(currentLevelTime, false));
            float retrievedBestValue = PlayerPrefs.GetFloat(_currentLevel.UniqueIDBest, 0f);
            float retrievedTotalValue = PlayerPrefs.GetFloat(_currentLevel.UniqueIDTotal, 0f);

            _newBestTimeLabel.SetActive(currentLevelTime.Equals(retrievedBestValue));
            _bestTimeLabel.SetText(GTWUtils.GetFormattedTimeOrDefaultString(retrievedBestValue, false));
            _totalTimeLabel.SetText(GTWUtils.GetFormattedTimeOrDefaultString(retrievedTotalValue, false));

            _levelTitleLabel.SetText(_currentLevel.fallbackLevelName);
            _selectedLevelImage.sprite = _currentLevel.LevelImage;

            if (_nextLevel)
            {
                _nextLevelButton.GetSelectable().Select();
            }
            else
            {
                _retryLevelButton.GetSelectable().Select();
            }
        }

        public override void Exit(MonoStateMachine controller)
        {
            Time.timeScale = 1f;
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            return this;
        }

        public override void ReturnFromSubState(MonoStateMachine controller, MonoState previousState)
        {
            base.ReturnFromSubState(controller, previousState);
            OpenPopup();
        }


        // EVENT HANDLING

        public void Button_NextLevel()
        {
            ClosePopup();
            RestoreUIContext();

            if (_nextLevel.gameMode != _currentLevel.gameMode)
            {
                GTWGameWorldDefinition worldDefinition = _doinklerPortfolioDefinition.GTWGameWorldDefinitions.First(x => x.GameLevels.Contains(_nextLevel));
                _gameState.SaveSlot = (int)worldDefinition.SaveSlotId;
                _gameState.GameLevelDefinition = _nextLevel;
                _doinklerWorldDefinitionProvider.Current = worldDefinition;
                _gameState.StartGameMode(_nextLevel.gameMode);
            }
            else
            {
                _gameState.GameLevelDefinition = _nextLevel;
                _checkpointManager.SetActiveCheckpoint(_checkpointManager.CheckpointData.levelCheckpointDatas[_nextLevel.CheckpointNumber]);
                Events.RaiseEvent(GTWEvents.DOINKLER_STAGE_RESET, false);
                _onRespawn?.Invoke();
            }
        }

        public void Button_RetryLevel()
        {
            ClosePopup();
            RestoreUIContext();
            Events.RaiseEvent(GTWEvents.DOINKLER_STAGE_RESET, false);
            _onRespawn?.Invoke();
        }

        public void Button_LevelSelect()
        {
            ClosePopup();
            _menuController.EnterSubState(_doinklerLevelSelectSubState);
        }

        public void Button_QuitToMain()
        {
            ClosePopup();
            RestoreUIContext();
            _gameState.LoadTitleScene();
        }


        // ACCESSORS

        public void SetLevelDefinitionData(GTWGameLevelDefinition currentLevel, GTWGameLevelDefinition nextLevel, Action onRespawn)
        {
            _currentLevel = currentLevel;
            _nextLevel = nextLevel;
            _onRespawn = onRespawn;

            OpenPopup();
            StealUIContext();
        }

        private float GetActivatedDoinklerDialoguesPercentage()
        {
            int totalCount = _levelVariables.boolVariables.Count;
            int activeCount = _levelVariables.boolVariables.Count(x => x.Value);
            float doinklerDialoguePercentage = 0f;

            if (activeCount != 0)
            {
                doinklerDialoguePercentage = (float) activeCount / totalCount;
            }
            return doinklerDialoguePercentage;
        }

        private float GetCompletedLevelsInFilePercentage(GTWGameWorldDefinition gtwGameWorldDefinition)
        {
            int totalLevelCount = gtwGameWorldDefinition.GameLevels.Count;
            int completedLevelCount = gtwGameWorldDefinition.GameLevels.Count(x => x.IsCompleted);
            float fileLevelCompletionPercentage = 0f;

            if (completedLevelCount != 0)
            {
                fileLevelCompletionPercentage = (float) completedLevelCount / totalLevelCount;
            }
            return fileLevelCompletionPercentage;
        }

        private float GetCompletedLevelsInDoinklerPercentage()
        {
            int totalDoinklerLevelCount = _doinklerPortfolioDefinition.GetTotalDoinklerLevelCount();
            int completedDoinklerLevelCount = _doinklerPortfolioDefinition.GetCompletedDoinklerLevelCount();
            float doinklerLevelCompletionPercentage = 0f;

            if (completedDoinklerLevelCount != 0)
            {
                doinklerLevelCompletionPercentage = (float) completedDoinklerLevelCount / totalDoinklerLevelCount;
            }
            return doinklerLevelCompletionPercentage;
        }



        // OTHER METHODS

        private void OpenPopup()
        {
            _canvasGroup.alpha = 1f;
            _canvasGroup.interactable = true;
            _canvasGroup.blocksRaycasts = true;
            _isOpen = true;
            _nextLevelButton.GetSelectable().interactable = _nextLevel;
        }

        private void ClosePopup()
        {
            _canvasGroup.alpha = 0f;
            _canvasGroup.interactable = false;
            _canvasGroup.blocksRaycasts = false;
            _isOpen = false;
        }

        private void RestoreUIContext()
        {
            Time.timeScale = 1f;
            _controls.SetControlMode(Controls.Mode.Gameplay);

            _menuController.ExitSubState();

            if (Controls.UsingController && _previousSelection != null)
            {
                EventSystem.current.SetSelectedGameObject(_previousSelection);
            }
        }

        private void StealUIContext()
        {
            if (_controls.GetControlMode() != Controls.Mode.UI)
            {
                _controls.SetControlMode(Controls.Mode.UI);
            }

            Time.timeScale = 0.00f;
        }
    }
}