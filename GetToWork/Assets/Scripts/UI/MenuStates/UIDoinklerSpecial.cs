// Copyright Isto Inc.

using Isto.Core;
using Isto.Core.Data;
using Isto.Core.Inputs;
using Isto.Core.StateMachine;
using Isto.Core.UI;
using Isto.GTW.Configuration;
using Isto.GTW.Data;
using Isto.GTW.Leaderboards;
using Steamworks;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Zenject;

namespace Isto.GTW.UI
{
    /// <summary>
    /// NOTE FROM STEPHEN - 2025-06-03
    /// I copied this script and modified it from UISetGameModeSubstate. Feel free to delete anything redundant
    /// </summary>
    public class UIDoinklerSpecial : CoreUISetGameModeSubState
    {
        // UNITY HOOKUP

        [Header("Confirm Dialogue")]
        [SerializeField] private UISimpleConfirmModalState _confirmationSubState;

        [Header("Leaderboard")]
        [SerializeField] private Transform _leaderboardContainer;
        [SerializeField] private GameObject _leaderboardEntryPrefab;

        [Header("Progress Report")]
        [SerializeField] private TextMeshProUGUI _employeeNameText;
        [SerializeField] private TextMeshProUGUI _totalTimeText;
        [SerializeField] private TextMeshProUGUI _currentCheckpointText;
        [SerializeField] private CoreButton _doinklerStartButton;

        [Header("Bottom of Folder")]
        [SerializeField] private CoreButton _resetProgressButton;

        [Header("Doinkler Special Data Hookup")]
        [SerializeField] private GTWGameLevelDefinition _gameLevelDefinition = null;


        // OTHER FIELDS

        private Selectable _currentSelection;
        private MonoPushdownStateMachine _menuController;
        private GTWGameLevelDefinition _selectedLevel = null;


        // INJECTION

        private IGameData _gameData;
        private GTWGameState _gameState;
        private ILeaderboard _doinklerSpecialLeaderboard;


        [Inject]
        public void Inject(GTWGameState gamestate, IGameData gameData, ILeaderboard doinklerSpecialLeaderboard)
        {
            _gameState = gamestate;
            _gameData = gameData;
            _doinklerSpecialLeaderboard = doinklerSpecialLeaderboard;
        }


        // LIFECYCLE EVENTS

        private void Awake()
        {
            Events.Subscribe(Events.INPUT_MODE_CHANGED, Events_OnInputModeChanged);
            _doinklerSpecialLeaderboard.OnCheckpointsDownloaded += LeaderboardManager_OnCheckpointsDownloaded;
        }

        private void OnDestroy()
        {
            _doinklerSpecialLeaderboard.OnCheckpointsDownloaded -= LeaderboardManager_OnCheckpointsDownloaded;
            Events.UnSubscribe(Events.INPUT_MODE_CHANGED, Events_OnInputModeChanged);
        }

        public override void Enter(MonoStateMachine controller)
        {
            base.Enter(controller);

            SetupStateMachineController(controller);

            SetupProgressReport();
            SetupCompanyLeaderboard();
            _currentSelection = _doinklerStartButton;
            _currentSelection.Select();
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            if (_controls.GetButtonDown(UserActions.UICANCEL))
            {
                _menuController.ExitSubState();
            }

            if (_controls.UsingJoystick() && EventSystem.current.currentSelectedGameObject == null)
            {
                HighlightFallbackSelectable();
            }

            return this;
        }

        public override void ReturnFromSubState(MonoStateMachine controller, MonoState previousState)
        {
            if (_controls.UsingJoystick())
            {
                if (EventSystem.current.currentSelectedGameObject == null)
                {
                    HighlightFallbackSelectable();
                }
            }
        }


        // EVENT HANDLING

        private void Events_OnInputModeChanged()
        {
            if (_controls.UsingJoystick() && _currentSelection != null)
            {
                _currentSelection.Select();
            }
            else if (!_controls.UsingJoystick() && EventSystem.current.currentSelectedGameObject != null)
            {
                EventSystem.current.SetSelectedGameObject(null);
            }
        }

        private void LeaderboardManager_OnCheckpointsDownloaded()
        {
            List<LeaderboardEntryData> entries = _doinklerSpecialLeaderboard.LeaderboardEntries;
            while (_leaderboardContainer.childCount > 0)
            {
                DestroyImmediate(_leaderboardContainer.GetChild(0).gameObject);
            }

            List<Selectable> leaderboardDisplaySelectables = new List<Selectable>();
            foreach (LeaderboardEntryData entry in entries)
            {
                GTWLeaderboardPlayerDisplay playerDisplay = Instantiate(_leaderboardEntryPrefab, _leaderboardContainer).GetComponent<GTWLeaderboardPlayerDisplay>();
                playerDisplay.Setup(entry, true);
                leaderboardDisplaySelectables.Add(playerDisplay.GetComponent<Selectable>());
            }

            SetupSelectables(leaderboardDisplaySelectables);
        }

        public void ResetProgress_ButtonClicked()
        {
            if (_controls.UsingJoystick() &&
                _resetProgressButton != null &&
                _resetProgressButton.gameObject.activeInHierarchy &&
                _resetProgressButton.interactable)
            {
                _currentSelection = _resetProgressButton;
                _currentSelection.Select();
            }

            _confirmationSubState.SetCallbacks(ConfirmationSubState_ResetTimesConfirmed, null);
            _menuController.EnterSubState(_confirmationSubState);
        }

        public void StartLevel_ButtonClicked()
        {
            _gameState.GameLevelDefinition = _gameLevelDefinition;
            int saveSlot = (int)GTWGameState.SaveSlotId.DOINKLER_SPECIAL;

            _gameState.LoadSaveSlotMetaData(saveSlot, (gameStateData) =>
            {
                _gameData.HasSaveData(saveSlot, (dataExists) =>
                {
                    if (dataExists)
                    {
                        _gameState.LoadSaveGame(saveSlot);
                    }
                    else
                    {
                        _gameState.SaveSlot = saveSlot;
                        _gameState.StartGameMode(_gameLevelDefinition.gameMode);
                    }
                });
            });
        }


        // OTHER METHODS

        private void ConfirmationSubState_ResetTimesConfirmed()
        {
            _gameData.ClearSaveData((int)GTWGameState.SaveSlotId.DOINKLER_SPECIAL, (_) =>
            {
                Debug.Log($"Cleared Save Data for saveslot {GTWGameState.SaveSlotId.DOINKLER_SPECIAL}");
            });
            StartLevel_ButtonClicked();
        }

        private void HighlightFallbackSelectable()
        {
            _currentSelection = _doinklerStartButton;
            _currentSelection.Select();
        }

        private void SetupCompanyLeaderboard()
        {
            _doinklerSpecialLeaderboard.DownloadFriendsEntries(1, 50);
        }

        private void SetupProgressReport()
        {
#if PLATFORM_STEAM
            if (SteamManager.Initialized)
            {
                _employeeNameText.text = SteamFriends.GetPersonaName();
            }
            else
            {
                _employeeNameText.text = "theDoinkler";
            }
#else
            _employeeNameText.text = "theDoinkler";
#endif
            _totalTimeText.text = "51 hr, 2 min, 4 sec";
            _currentCheckpointText.text = "5/45";
        }

        private void SetupStateMachineController(MonoStateMachine controller)
        {
            if (controller is MonoPushdownStateMachine stateMachine)
            {
                _menuController = stateMachine;
            }
            else
            {
                Debug.LogError("Using level select menu without a push down menu controller, this won't work");
            }
        }

        private void SetupSelectables(List<Selectable> leaderboardDisplaySelectables)
        {
            for (int i = 0; i < leaderboardDisplaySelectables.Count; i++)
            {
                Selectable displaySelectable = leaderboardDisplaySelectables[i];

                if (i == 0)
                {
                    UIUtils.SetNavigation(displaySelectable, UIUtils.NavigationDirection.Down, leaderboardDisplaySelectables[i + 1]);
                }
                else if (i == leaderboardDisplaySelectables.Count - 1)
                {
                    UIUtils.SetNavigation(displaySelectable, UIUtils.NavigationDirection.Up, leaderboardDisplaySelectables[i - 1]);
                }
                else
                {
                    UIUtils.SetNavigation(displaySelectable, UIUtils.NavigationDirection.Down, leaderboardDisplaySelectables[i + 1]);
                    UIUtils.SetNavigation(displaySelectable, UIUtils.NavigationDirection.Up, leaderboardDisplaySelectables[i - 1]);
                }

                UIUtils.SetNavigation(displaySelectable, UIUtils.NavigationDirection.Right, _doinklerStartButton);
            }

            if (leaderboardDisplaySelectables.Count > 0)
            {
                UIUtils.SetNavigation(_doinklerStartButton, UIUtils.NavigationDirection.Left, leaderboardDisplaySelectables[0]);
            }
        }
    }
}