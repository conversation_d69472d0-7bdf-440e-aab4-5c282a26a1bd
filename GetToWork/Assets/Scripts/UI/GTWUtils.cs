// Copyright Isto Inc.

using System;
using UnityEngine;
using Zenject;

namespace Isto.GTW.UI
{
    /// <summary>
    /// Helpers methods for common operations we perform in code specific to GTW
    /// </summary>
    public static class GTWUtils
    {

        /// <summary>
        /// Converts a string into either a time in the two digit format("HH:MM:SS.MS")
        /// or returns the string "--" if it is anything but a time in seconds.
        /// It also applies a grey color to any values that are 0 and shrinks the milliseconds.
        /// </summary>
        /// <param name="inputTime">Time in seconds or "--"</param>
        /// <returns>A string that represents the total hours, minutes, and seconds of the specified string.</returns>
        public static string GetFormattedTimeOrDefaultString(string inputTime, bool greyOutZeros = true)
        {
            string greyedOutTextColor = "C3C3C3";
            if (!greyOutZeros)
                greyedOutTextColor = ColorUtility.ToHtmlStringRGB(Color.black);

            string hourColor = ColorUtility.ToHtmlStringRGB(Color.black);
            string minuteColor = ColorUtility.ToHtmlStringRGB(Color.black);



            if (float.TryParse(inputTime, out float timeinLevel))
            {
                TimeSpan time = TimeSpan.FromSeconds(timeinLevel);
                if ((int)time.TotalHours == 0)
                {
                    hourColor = greyedOutTextColor;
                }
                if ((int)time.TotalMinutes == 0)
                {
                    minuteColor = greyedOutTextColor;
                }

                return string.Format("<color=#{0}>{1:D2}:</color><color=#{2}>{3:D2}:</color>{4:D2}.<size=70%>{5:D2}</size>",
                    hourColor,
                    (int)time.TotalHours,
                    minuteColor,
                    time.Minutes,
                    time.Seconds,
                    time.Milliseconds / 10);
            }
            else
            {
                return string.Format("<color=#{0}>--</color>",
                    greyedOutTextColor);
            }
        }
        public static string GetFormattedTimeOrDefaultString(float inputTime, bool greyOutZeros = true)
        {
            return GetFormattedTimeOrDefaultString(inputTime.ToString(), greyOutZeros);
        }

        public static Texture2D FlipTextureVertically(Texture2D original)
        {
            Texture2D flipped = new Texture2D(original.width, original.height, original.format, false);
            int width = original.width;
            int height = original.height;

            for (int y = 0; y < height; y++)
            {
                // Get one row from the original and assign it to the opposite row in the flipped texture.
                Color[] row = original.GetPixels(0, y, width, 1);
                flipped.SetPixels(0, height - y - 1, width, 1, row);
            }
            flipped.Apply();
            return flipped;
        }

    }
}