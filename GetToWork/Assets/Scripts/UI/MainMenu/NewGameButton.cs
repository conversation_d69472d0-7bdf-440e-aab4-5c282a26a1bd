// Copyright Isto Inc.

using I2.Loc;
using Isto.Core.Configuration;
using Isto.Core.Data;
using Isto.Core.Speedrun;
using Isto.Core.UI;
using UnityEngine;
using Zenject;

namespace Isto.GTW.UI
{
    public class NewGameButton : MonoBehaviour
    {
        [SerializeField] private GameModeDefinition _gameMode = null;
        [SerializeField] private GTWGameState.SaveSlotId _saveSlot = GTWGameState.SaveSlotId.MAIN_GAME;

        [Header("Save Data Override popup config")]
        [SerializeField] private LocalizedString _title;
        [SerializeField] private LocalizedString _description;
        [SerializeField] private LocalizedString _question;

        [Header("Speedrun Settings for Main Gameplay")]
        [SerializeField] SpeedrunSettings _mainGameSpeedrunSettings;

        // INJECTION

        private GTWGameState _gameState;
        private IGameData _gameData;
        private UIModalChoicePopup _choicePopup;

        [Inject]
        public void Inject(IGameData gameData,
            GTWGameState gameState, UIModalChoicePopup choicePopup)
        {
            _gameState = gameState;
            _gameData = gameData;
            _choicePopup = choicePopup;
        }


        // Callbacks

        public void Button_OnClick()
        {
            _gameState.GameLevelDefinition = null;
            RebindSpeedrunSettings();
            int slot = (int)_saveSlot;
            _gameState.LoadSaveSlotMetaData(slot, (gameStateData) =>
            {
                _gameData.HasSaveData(slot, (dataExists) =>
                {
                    if (dataExists)
                    {
                        ShowOverridePopup();
                    }
                    else
                    {
                        StartGame();
                    }
                });
            });
        }

        private void OnModalChoicePopup_Yes()
        {
            StartGame();
        }

        private void OnModalChoicePopup_No()
        {
            // abort the new game
        }


        // OTHER METHODS

        private void ShowOverridePopup()
        {
            _choicePopup.DisplayChoice(UIModalChoicePopup.ChoicePopupType.YesNoChoice,
                        _question,
                        OnModalChoicePopup_Yes, OnModalChoicePopup_No,
                        _description, _title);
        }

        private void StartGame()
        {
            _gameState.SaveSlot = (int)_saveSlot;
            _gameState.StartGameMode(_gameMode);
        }

        private void RebindSpeedrunSettings()
        {
            DiContainer projectContainer = ProjectContext.Instance.Container;
            projectContainer.Rebind(typeof(SpeedrunSettings))
                .FromInstance(_mainGameSpeedrunSettings)
                .AsCached()
                .NonLazy();
            projectContainer.Inject(_gameData);
        }
    }
}