// Copyright Isto Inc.

using Isto.GTW.Data;
using Isto.GTW.Leaderboards;
using Isto.GTW.LevelFeatures;
using TMPro;
using UnityEngine;
using Zenject;

namespace Isto.GTW.UI
{
    public class UIDoinklerLeaderboardCheckpoint : MonoBehaviour
    {
        // UNITY HOOKUP

        [SerializeField] private GameObject _instructionsContainer;
        [SerializeField] private Checkpoint _checkpoint;
        [SerializeField] private TextMeshProUGUI _playersCompletedText;


        // INJECTION

        ILeaderboard _leaderboard;
        private CheckpointManager _checkpointManager;


        [Inject]
        public void Inject(ILeaderboard leaderboard, CheckpointManager checkpointManager)
        {
            _leaderboard = leaderboard;
            _checkpointManager = checkpointManager;
        }


        // LIFECYCLE EVENTS

        private void Awake()
        {
            // We only show the checkpoint display on Steam
#if PLATFORM_STEAM
            SetupLeaderboard();
#elif PLATFORM_GAMECORE
            _instructionsContainer.SetActive(false);
#endif
        }


        // OTHER METHODS

        private void SetupLeaderboard()
        {
            int checkpointId = _checkpointManager.CheckpointData.GetCheckpointId(_checkpoint);
            var leaderboardEntries = _leaderboard.LeaderboardEntries.FindAll(x => x.HighestCheckpoint == checkpointId);

            if(leaderboardEntries.Count == 0)
            {
                _playersCompletedText.text = "0 players have completed this checkpoint, disabling the checkpoint display.";
                _instructionsContainer.SetActive(false);
            }
            else
            {
                string playerNames = "";
                foreach (LeaderboardEntryData leaderboardEntryData in leaderboardEntries)
                {
                    playerNames += leaderboardEntryData.PlayerName + "\n";
                }
                playerNames = playerNames.TrimEnd('\n');
                _playersCompletedText.text = playerNames;
            }

        }
    }
}