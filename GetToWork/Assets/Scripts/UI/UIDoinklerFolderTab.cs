// Copyright Isto Inc.

using Isto.Core.UI;
using Isto.GTW.UI;
using UnityEngine;
using UnityEngine.UI;

public class UIDoinklerFolderTab : MonoBehaviour
{

    // UNITY HOOKUP

    [SerializeField] protected GameObject shadowImage;
    [SerializeField] private Image folderTabImage;

    [SerializeField] private Color _activeColor = Color.white;
    [SerializeField] private Color _inactiveColor = new Color(0.92f, 0.92f, 0.92f);


    // OTHER FIELDS

    private int _folderTabNumber;
    private bool _isActive;
    private CoreButtonRectPosition _corebuttonRect;
    private UISetGameModeSubState _parentMenu = null;

    // PROPERTIES


    // EVENTS


    // INJECTION

    public void Setup(UISetGameModeSubState parentMenu, int folderTabNum)
    {
        _parentMenu = parentMenu;
        _folderTabNumber = folderTabNum;
        _corebuttonRect = this.GetComponent<CoreButtonRectPosition>();
    }

    public void NewTabActive(int activeTabNumber)
    {
        Vector3 position = this.transform.position;


        if (activeTabNumber == _folderTabNumber)
        {
            if (!_isActive)
            {
                //Setup
                _isActive = true;
                //position.y += 10;
                this.transform.position = position;
            }
            folderTabImage.color = _activeColor;

            shadowImage.SetActive(false);
            _corebuttonRect.enabled = false;
        }
        else
        {
            folderTabImage.color = _inactiveColor;
            if (_isActive)
            {
                _isActive = false;

                //position.y -= 10;
                this.transform.position = position;
            }
            //Go back to normal

            shadowImage.SetActive(true);
            _corebuttonRect.enabled = true;
        }
    }


    // EVENT HANDLING

    public void FolderTab_OnClick()
    {
        _parentMenu.FolderTab_OnClick(_folderTabNumber);
    }


    // ACCESSORS


    // OTHER METHODS

}