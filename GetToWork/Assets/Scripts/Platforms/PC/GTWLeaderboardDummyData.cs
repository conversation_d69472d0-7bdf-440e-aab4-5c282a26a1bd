// Copyright Isto Inc.

using Isto.GTW.Data;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.GTW.Leaderboards
{
    [CreateAssetMenu(fileName = nameof(GTWLeaderboardDummyData), menuName = "Scriptables/Leaderboards/Leaderboard Dummy Data")]
    public class GTWLeaderboardDummyData : ScriptableObject
    {
        [SerializeField]
        private List<LeaderboardEntryData> _entries = new List<LeaderboardEntryData>();

        public List<LeaderboardEntryData> Entries => _entries;
    }
}