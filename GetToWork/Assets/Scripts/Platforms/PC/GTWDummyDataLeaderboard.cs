// Copyright Isto Inc.

using Isto.GTW.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Isto.GTW.Leaderboards
{
    public class GTWDummyDataLeaderboard : MonoBehaviour, ILeaderboard
    {
        // UNITY HOOKUP

        [Header("Leaderboard Dummy Data")]
        [SerializeField] private GTWLeaderboardDummyData _gtwLeaderboardDummyData;


        // OTHER FIELDS

        private List<LeaderboardEntryData> _leaderboardEntries = new List<LeaderboardEntryData>();


        // PROPERTIES

        public List<LeaderboardEntryData> LeaderboardEntries => _leaderboardEntries;


        // EVENTS

        public event Action OnCheckpointsDownloaded;


        // LIFECYCLE EVENTS

        private void Start()
        {
            _gtwLeaderboardDummyData = Resources.Load<GTWLeaderboardDummyData>("GTWLeaderboardDummyData");
        }


        // OTHER METHODS

        public void UploadHighestCheckpoint(int score, bool forceUpload = false)
        {
            int highestScore = PlayerPrefs.GetInt(ILeaderboard.HIGHEST_CHECKPOINT_PLAYER_PREFS_KEY, 0);
            if (forceUpload || score > highestScore)
            {
                PlayerPrefs.SetInt(ILeaderboard.HIGHEST_CHECKPOINT_PLAYER_PREFS_KEY, score);
            }
        }

        public void DownloadFriendsEntries(int startRank, int endRank)
        {
            int highestScore = PlayerPrefs.GetInt(ILeaderboard.HIGHEST_CHECKPOINT_PLAYER_PREFS_KEY, 0);
            LeaderboardEntryData currentPlayer = new LeaderboardEntryData
            (
                "theDoinkler", // Stephen needs to decide the player name
                highestScore, // TODO: Pull this from PlayerPrefs
                0, // This can remain as 0
                0 // This gets adjusted later
            );


            List<LeaderboardEntryData> currentLeaderboardEntries = _gtwLeaderboardDummyData.Entries;
            currentLeaderboardEntries.RemoveAll(x => x.PlayerName == currentPlayer.PlayerName);
            currentLeaderboardEntries.Add(currentPlayer);
            currentLeaderboardEntries = currentLeaderboardEntries.OrderByDescending(x => x.HighestCheckpoint).ToList();


            int rank = 1;
            foreach (LeaderboardEntryData entry in currentLeaderboardEntries)
            {
                entry.LocalRank = rank;
                rank++;
            }

            _leaderboardEntries = currentLeaderboardEntries;

            OnCheckpointsDownloaded?.Invoke();
        }
    }
}