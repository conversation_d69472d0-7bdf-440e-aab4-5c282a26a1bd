// Copyright Isto Inc.

using Isto.GTW.Data;
using Isto.GTW.Leaderboards;
using Isto.GTW.UI;
using Steamworks;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.GTW.Managers
{
    public class GTWSteamLeaderboard : MonoBehaviour, ILeaderboard
    {
        // UNITY HOOKUP

        [Header("Steamworks Leaderboard Name")]
        [SerializeField] private string _leaderboardName = "DoinklerSpecial_HighestCheckpoint";


        // OTHER FIELDS

        private CallResult<LeaderboardScoresDownloaded_t> _downloadScoresResult;
        private CallResult<LeaderboardFindResult_t> _findLeaderboardResult;
        private CallResult<LeaderboardScoreUploaded_t> _uploadScoreResult;
        private SteamLeaderboard_t _steamLeaderboard;
        private List<LeaderboardEntryData> _leaderboardEntries = new List<LeaderboardEntryData>();
        private bool _leaderboardFound;


        // PROPERTIES

        public List<LeaderboardEntryData> LeaderboardEntries => _leaderboardEntries;


        // EVENTS

        public event Action OnCheckpointsDownloaded;


        // LIFECYCLE EVENTS

        private void Start()
        {
            if (!SteamManager.Initialized)
            {
                Debug.LogError("[LeaderboardManager] SteamManager not initialized. Cannot find leaderboard.");
                return;
            }

            _findLeaderboardResult = CallResult<LeaderboardFindResult_t>.Create(OnFindLeaderboard);
            _uploadScoreResult = CallResult<LeaderboardScoreUploaded_t>.Create(OnUploadScore);
            _downloadScoresResult = CallResult<LeaderboardScoresDownloaded_t>.Create(OnDownloadScores);

            SteamAPICall_t handle = SteamUserStats.FindLeaderboard(_leaderboardName);
            _findLeaderboardResult.Set(handle);

            Debug.Log($"[LeaderboardManager] Attempting to find leaderboard '{_leaderboardName}'...");
        }

        private void OnDestroy()
        {
            _findLeaderboardResult?.Cancel();
            _uploadScoreResult?.Cancel();
            _downloadScoresResult?.Cancel();
        }

        // EVENT HANDLING

        private void OnFindLeaderboard(LeaderboardFindResult_t result, bool isIOFailure)
        {
            if (isIOFailure || result.m_bLeaderboardFound == 0)
            {
                Debug.LogError($"[LeaderboardManager] Failed to find leaderboard '{_leaderboardName}'. IOFailure? {isIOFailure}");
                return;
            }

            _steamLeaderboard = result.m_hSteamLeaderboard;
            _leaderboardFound = true;
            Debug.Log($"[LeaderboardManager] Found leaderboard '{_leaderboardName}' (Handle: {_steamLeaderboard.m_SteamLeaderboard}).");
        }

        private void OnUploadScore(LeaderboardScoreUploaded_t result, bool isIOFailure)
        {
            if (isIOFailure || result.m_bSuccess == 0)
            {
                Debug.LogError($"[LeaderboardManager] Score upload failed. IOFailure? {isIOFailure}");
                return;
            }

            Debug.Log($"[LeaderboardManager] Upload succeeded. New rank: {result.m_nGlobalRankNew}, Score: {result.m_nScore}");
        }

        private void OnDownloadScores(LeaderboardScoresDownloaded_t result, bool isIOFailure)
        {
            if (isIOFailure)
            {
                Debug.LogError($"[LeaderboardManager] Failed to download leaderboard entries. IOFailure? {isIOFailure}");
                return;
            }

            int count = result.m_cEntryCount;
            List<LeaderboardEntryData> entries = new List<LeaderboardEntryData>(count);

            for (int i = 0; i < count; i++)
            {
                LeaderboardEntry_t entry;
                SteamUserStats.GetDownloadedLeaderboardEntry(
                    result.m_hSteamLeaderboardEntries,
                    i,
                    out entry,
                    null,
                    0
                );

                string playerName = SteamFriends.GetFriendPersonaName(entry.m_steamIDUser);
                int globalRank = entry.m_nGlobalRank;
                int localRank = i + 1;

                Texture2D avatarTex = GetPlayerAvatar(entry);

                LeaderboardEntryData data = new LeaderboardEntryData(
                    playerName,
                    entry.m_nScore,
                    globalRank,
                    localRank,
                    avatarTex
                );
                entries.Add(data);
            }

            EvaluateAndUploadHighestScore(entries);

            _leaderboardEntries = entries;

            OnCheckpointsDownloaded?.Invoke();
        }


        // ACCESSORS

        private static Texture2D GetPlayerAvatar(LeaderboardEntry_t entry)
        {
            SteamFriends.RequestUserInformation(entry.m_steamIDUser, true);

            int avatarHandle = SteamFriends.GetLargeFriendAvatar(entry.m_steamIDUser);
            Texture2D avatarTextyre = null;

            if (avatarHandle != -1)
            {
                if (SteamUtils.GetImageSize(avatarHandle, out uint width, out uint height))
                {
                    byte[] raw = new byte[width * height * 4]; // We multiply by 4 as RGBA is 4 bytes per pixel
                    if (SteamUtils.GetImageRGBA(avatarHandle, raw, raw.Length))
                    {
                        avatarTextyre = new Texture2D((int)width, (int)height, TextureFormat.RGBA32, false);
                        avatarTextyre.LoadRawTextureData(raw);
                        avatarTextyre.Apply();

                        // Since the coordinates are different between Unity and Steam, we need to flip the texture vertically
                        avatarTextyre = GTWUtils.FlipTextureVertically(avatarTextyre);

                    }
                }
            }

            return avatarTextyre;
        }


        // OTHER METHODS

        public void UploadHighestCheckpoint(int score, bool forceUpload = false)
        {
            if (!_leaderboardFound)
            {
                Debug.LogError("[LeaderboardManager] UploadScore called too early. Leaderboard not yet found.");
                return;
            }

            // We have to check player prefs for the highest score, and only upload if the new score is higher
            // The reason is we are using k_ELeaderboardUploadScoreMethodForceUpdate for the update method
            // as the other enums aren't reliable.
            int highestScore = PlayerPrefs.GetInt(ILeaderboard.HIGHEST_CHECKPOINT_PLAYER_PREFS_KEY, 0);
            if (forceUpload || score >= highestScore)
            {
                PlayerPrefs.SetInt(ILeaderboard.HIGHEST_CHECKPOINT_PLAYER_PREFS_KEY, score);
                SteamAPICall_t call = SteamUserStats.UploadLeaderboardScore(
                    _steamLeaderboard,
                    ELeaderboardUploadScoreMethod.k_ELeaderboardUploadScoreMethodForceUpdate,
                    score,
                    null,
                    0
                );
                _uploadScoreResult.Set(call);
                Debug.Log($"[LeaderboardManager] Uploading score {score}...");
            }
        }

        public void DownloadFriendsEntries(int startRank, int endRank)
        {
            if (!_leaderboardFound)
            {
                Debug.LogError("[LeaderboardManager] DownloadTopEntries called too early. Leaderboard not yet found.");
                return;
            }

            SteamAPICall_t call = SteamUserStats.DownloadLeaderboardEntries(
                _steamLeaderboard,
                ELeaderboardDataRequest.k_ELeaderboardDataRequestFriends,
                startRank,
                endRank
            );

            _downloadScoresResult.Set(call);
            Debug.Log($"[LeaderboardManager] Requesting entries {startRank}–{endRank}...");
        }

        /// <summary>
        /// This helper method is used to ensure that, if the players highest score uploads didn't go through during
        /// the Doinkler special, we will check and add it to the leaderboard if needed.
        /// </summary>
        /// <param name="entries">The leaderboard entries to evaluate</param>
        private void EvaluateAndUploadHighestScore(List<LeaderboardEntryData> entries)
        {
            LeaderboardEntryData playerEntryData = entries.Find(x => x.PlayerName == SteamFriends.GetPersonaName());
            int savedHighestScore = PlayerPrefs.GetInt(ILeaderboard.HIGHEST_CHECKPOINT_PLAYER_PREFS_KEY, 0);
            if(playerEntryData != null)
            {
                if(playerEntryData.HighestCheckpoint < savedHighestScore)
                {
                    UploadHighestCheckpoint(savedHighestScore);
                }
            }
            else
            {
                UploadHighestCheckpoint(savedHighestScore, true);
            }
        }
    }
}