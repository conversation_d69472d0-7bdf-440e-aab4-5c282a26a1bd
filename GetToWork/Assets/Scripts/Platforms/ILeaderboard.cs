// Copyright Isto Inc.

using Isto.GTW.Data;
using System;
using System.Collections.Generic;

namespace Isto.GTW.Leaderboards
{
    public interface ILeaderboard
    {
        protected static string HIGHEST_CHECKPOINT_PLAYER_PREFS_KEY = "DOINKLER_SPECIAL_HIGHEST_CHECKPOINT";
        public List<LeaderboardEntryData> LeaderboardEntries { get;}
        public void UploadHighestCheckpoint(int score, bool forceUpload = false);
        public void DownloadFriendsEntries(int startRank, int endRank);
        public event Action OnCheckpointsDownloaded;
    }
}