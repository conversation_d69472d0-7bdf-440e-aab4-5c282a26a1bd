// Copyright Isto Inc.

using Isto.Core;
using Isto.Core.Audio;
using Isto.Core.StateMachine;
using Isto.Core.UI;
using Isto.GTW;
using Isto.GTW.Audio;
using Isto.GTW.UI;
using PixelCrushers.DialogueSystem;
using UnityEngine;
using UnityEngine.UI;
using Zenject;

public class GTWGameMenuState : UISimpleGameMenuState
{
    // UNITY HOOKUP

    public MonoState levelSelectSubMenu;

    public Button saveSplitsButton;
    public Button levelSelectButton;


    // OTHER FIELDS


    // INJECTION

    private GTWGameSounds _sounds;
    private GTWGameState _gtwGameState;

    [Inject]
    private void Inject(IGameSounds sounds, GTWGameState gtwGameState)
    {
        _sounds = sounds as GTWGameSounds;
        _gtwGameState = gtwGameState;
    }


    // LIFECYCLE EVENTS

    public override void Enter(MonoStateMachine controller)
    {
        base.Enter(controller);

        DialogueManager.instance.Pause();
        _sounds.SetGameplaySoundsPaused(true);
    }

    public override void Exit(MonoStateMachine controller)
    {
        base.Exit(controller);

        DialogueManager.instance.Unpause();
        _sounds.SetGameplaySoundsPaused(false);
    }

    public override void ReturnFromSubState(MonoStateMachine controller, MonoState previousState)
    {
        base.ReturnFromSubState(controller, previousState);
    }


    // EVENT HANDLERS

    public void Button_OnSaveSplits()
    {
        Events.RaiseEvent(Events.SAVE_SPEEDRUN_BUTTON_CLICKED);
    }

    public void Button_OnLevelSelect()
    {
        if (_mainMenu.CurrentState != levelSelectSubMenu)
        {
            _mainMenu.EnterSubState(levelSelectSubMenu);
            _controls.DisableControls(Constants.CANVAS_FADE_TIME);
        }
    }


    // other methods

    protected override void UpdateActiveButtons()
    {
        // This is never in use in GTW.
        // Core pause menu allows you disable them per the game mode, but in our case they should not be ever shown.
        loadButton.gameObject.SetActive(false);
        saveButton.gameObject.SetActive(false);

        // logic copied from core
        bool restartAllowed = IsRestartButtonAllowed();
        restartButton.interactable = restartAllowed;
        restartButton.gameObject.SetActive(restartAllowed);

        // save splits button currently unique to GTW but we probably would want to move that in core
        if (saveSplitsButton != null)
        {
            bool saveSplitsPossible = IsSaveBestTimesButtonAllowed();

            // TODO: consider using the speedrun settings to decide if we show the save button?
            // this setting is intended to control the button on the timer panel, do we still use it?
            //saveSplitsPossible |= _speedrunSettings.GetTimerSetup(0).enablePersonalBestSaveButton;
            // or do we make a new setting for this one? one that would not be tied to a style.
            //saveSplitsPossible |= _speedrunSettings.enablePersonalBestSaveButtonInPauseMenu;
            // or do we tie it to when PB is allowed in general?
            //saveSplitsPossible |= _speedrunSettings.GetTimerSetup(0).enablePersonalBestDisplay;
            // or do we just let it always shown as it is now? maybe that's fine. we did record your times even if
            // you did not have the timer up. so we can offer you to save them at any time too. I think.

#if UNITY_GAMECORE
            // Not supported on xbox yet.
            saveSplitsPossible = false;
#endif
            saveSplitsButton.interactable = saveSplitsPossible;
            saveSplitsButton.gameObject.SetActive(saveSplitsPossible);


        }

        if (levelSelectButton != null)
        {
            bool levelSelectEnabled = IsLevelSelectButtonAllowed();

            levelSelectButton.interactable = levelSelectEnabled;
            levelSelectButton.gameObject.SetActive(levelSelectEnabled);
        }
    }

    protected override bool IsRestartButtonAllowed()
    {
        bool allowed = base.IsRestartButtonAllowed();

        if (allowed)
        {
            allowed = PlayerPrefs.GetInt(GTWUISettingsGameplaySubState.SHOW_RESTART_BUTTON_PREFS_KEY, GTWUISettingsGameplaySubState.DEFAULT_SHOW_RESTART_BUTTON) == 1;
        }

        return allowed;
    }

    protected bool IsSaveBestTimesButtonAllowed()
    {
        if(_gtwGameState.IsDoinklerSpecial)
            return false;

        bool allowed = PlayerPrefs.GetInt(GTWUISettingsGameplaySubState.SHOW_SAVE_PB_BUTTON_PREFS_KEY, GTWUISettingsGameplaySubState.DEFAULT_SHOW_SAVE_PB_BUTTON) == 1;

        return allowed;
    }

    protected bool IsLevelSelectButtonAllowed()
    {
        bool allowed = _gtwGameState.IsDoinklerPortfolio;
        return allowed;
    }

    protected override void QuitConfirmed()
    {
        if (_gtwGameState.GameLevelDefinition != null && _gtwGameState.GameLevelDefinition.saveOnQuit)
        {
            AutoSave();
        }

        _sounds.StopAllEventInstances();
        base.QuitConfirmed();
    }

    private void AutoSave()
    {
        Debug.Log("ActivateCheckpointEvent.AutoSave()");

        Events.RaiseEvent(Events.GAME_AUTOSAVING); // For indicator

        int slotNumber = _gtwGameState.SaveSlot;

        _gameState.SelectSaveGame(slotNumber);

        // TODO: I would like to understand why we need to do this?
        _gameState.LoadGameStateData(null, -1);

        _gameData.SaveGameData(slotNumber, false, success =>
        {
            if (success)
            {
                Events.RaiseEvent(Events.GAME_SAVED);

                // TODO: I don't think we need this
                _gameState.LoadSaveSlotMetaData(slotNumber, null);
            }
            else
            {
                // TODO?
                // The internal cause of the error should be causing an error dialog popup to happen
                Debug.LogError($"SaveGameData for slot#{slotNumber} has failed ");
            }
        }, false);
    }
}