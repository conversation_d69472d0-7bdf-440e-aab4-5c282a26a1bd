// Copyright Isto Inc.

using I2.Loc;
using Isto.Core.Configuration;
using Isto.GTW.Managers;
using UnityEngine;

namespace Isto.GTW.Configuration
{
    /// <summary>
    /// This scriptable object holds the level information that is needed for the user. We may need to add a few things
    /// to this such as the Scene name or a GameModeDefinition to load the correct scene. After that, we will need to
    /// apply the CheckPoint number on load. Everything else should be used as a display to the user.
    /// </summary>

    [CreateAssetMenu(fileName = "Scriptables/New GTW Game Mode Definition", menuName = "Scriptables/New GTW Game Mode Definition")]
    public class GTWGameLevelDefinition : ScriptableObject
    {

        [Header("Player Facing")]
        public LocalizedString LevelName; // Name of the checkpoint. Currently, we don't have Loc for the names so use a temporary one for now
        [Tooltip("In case the name of the level has not yet been localized, fallback to this")]
        public string fallbackLevelName;
        public int LevelNumber;
        public string TotalTimeInLevel = "--";
        public string FastestCompletedTime = "--"; // This is the time that will be displayed when the user completes something. Will need to load into this from save data.
        public bool IsCompleted; // This determines if the level has been completed by the user. This will gate the user from future progression. Also need to load from save data.
        public bool IsUnlocked; // Not saved, but determined at run time based on the completion of other levels.
        public Sprite LevelImage; // This is the sprite that will be used to showcase the current level that the user is looking at.

        [Space]
        public bool loadPlayerPositionFromSave = false;
        public bool saveOnQuit = false;


        [Header("Internal")]
        public GameModeDefinition gameMode;
        [Tooltip("Checkpoints start at 0, level numbers start at 1")]
        public int CheckpointNumber; // The number related to the checkpoint in the scene for loading purposes
        private string _uniqueID => gameMode.internalName + "_" + LevelNumber;
        public string UniqueIDBest => _uniqueID + "_BEST";
        public string UniqueIDTotal => _uniqueID + "_TOTAL";

        public void Setup()
        {
            float retrievedBestValue = PlayerPrefs.GetFloat(UniqueIDBest, 0f);
            float retrievedTotalValue = PlayerPrefs.GetFloat(UniqueIDTotal, 0f);

            if (retrievedBestValue > 0f)
            {
                FastestCompletedTime = retrievedBestValue.ToString("0.00");
                TotalTimeInLevel = retrievedTotalValue > 0f ? retrievedTotalValue.ToString("0.00") : "--";
                IsCompleted = true;
            }
            else
            {
                IsCompleted = false;
                FastestCompletedTime = "--";
                TotalTimeInLevel = "--";
            }
        }

        public void ResetTotalTimeProgress()
        {
            // We only want to reset the total time of the level, not the best time.
            PlayerPrefs.SetFloat(UniqueIDTotal, 0f);
            Setup();
        }
    }
}