// Copyright Isto Inc.

using System.Collections.Generic;
using System.IO;
using System.Xml.Serialization;
using Isto.Core.Data;
using Isto.GTW.LevelFeatures;
using Isto.GTW.Managers;
using Isto.GTW.Player;
using UnityEngine;
using Zenject;

namespace Isto.GTW.Data
{
    public class PlayerDataManager : DataManager, IDataLoadCompleteHandler
    {
        private const string FILE_NAME = "player";
        private const string FILE_PREFIX = "/" + FILE_NAME + "_";

        public override string FilePrefix => FILE_PREFIX;
        public override string BlobName => FILE_NAME;


        // PROPERTIES

        public CheckpointManager CheckpointManager => _playerControl.CheckpointManager;


        // INJECTION

        private PlayerController _playerControl;
        private TimeManager _timeManager;
        private GTWGameStats _gtwGameStats;
        private GTWGameState _gameState;



        [Inject]
        public void Inject(PlayerController playerController,
            TimeManager timeManager, GTWGameStats gtwGameStats, GTWGameState gameState)
        {
            _playerControl = playerController;
            _timeManager = timeManager;
            _gtwGameStats = gtwGameStats;
            _gameState = gameState;
        }

        public override bool Validate(in TextReader reader, in object previousDataObject)
        {
            bool valid = false;

            PlayerSaveData previousData = previousDataObject as PlayerSaveData;
            PlayerSaveData loadedData = LoadXMLFile<PlayerSaveData>(reader);

            if (loadedData != null)
            {
                valid = previousData.ContentEquals(loadedData);
            }

            return valid;
        }

        public override bool Load(in TextReader reader)
        {
            bool success = false;
            PlayerSaveData playerData = LoadXMLFile<PlayerSaveData>(reader);

            if (playerData != null)
            {
                if (playerData.checkpointId >= 0 && playerData.checkpointId < CheckpointManager.CheckpointData.levelCheckpointDatas.Count)
                {
                    CheckpointData.LevelCheckpointData current = null;

                    // Activate all past seen checkpoints first
                    if (playerData.seenCheckpoints != null)
                    {
                        for (int i = 0; i < playerData.seenCheckpoints.Count; i++)
                        {
                            int id = playerData.seenCheckpoints[i];
                            current = CheckpointManager.CheckpointData.levelCheckpointDatas[id];
                            CheckpointManager.SetActiveCheckpoint(current, idCheck: false);
                        }
                    }

                    current = CheckpointManager.CheckpointData.levelCheckpointDatas[playerData.checkpointId];
                    // If there is a Game Level Definition then we are NOT playing the main game and need
                    // to load that specific level.
                    if (_gameState.GameLevelDefinition != null)
                    {
                        int checkpointNumber = _gameState.GameLevelDefinition.CheckpointNumber;
                        if (checkpointNumber >= 0 &&
                            checkpointNumber < CheckpointManager.CheckpointData.levelCheckpointDatas.Count)
                        {
                            current = CheckpointManager.CheckpointData.levelCheckpointDatas[checkpointNumber];
                        }
                    }
                    CheckpointManager.SetActiveCheckpoint(current, idCheck: false);
                }

                bool loadPlayerPositionFromSave = false;
                if (_gameState.GameLevelDefinition != null)
                {
                    loadPlayerPositionFromSave = _gameState.GameLevelDefinition.loadPlayerPositionFromSave;
                }

                if (loadPlayerPositionFromSave)
                {
                    _playerControl.TeleportCharacter(playerData.position, playerData.forward);
                }
                else
                {
                    _playerControl.TeleportCharacter(CheckpointManager.ActiveCheckpoint.position, CheckpointManager.ActiveCheckpoint.forward);
                }

                _playerControl.ResetVelocityToZero();
                _playerControl.SetVelocity(playerData.velocity, playerData.angularVelocity);
                _playerControl.ChangeToWaitForSectionLoadingState(playerData.velocity, playerData.angularVelocity);
                _playerControl.ragDollAbilityAvailable = playerData.giveUpAbility;

                _timeManager.TotalGameTime = playerData.totalGameTime;
                _timeManager.TotalGameTimeUnscaled = playerData.totalGameTimeUnscaled;

                _gtwGameStats.TotalHeightGained = playerData.totalHeightGained;
                _gtwGameStats.TotalDistanceFallen = playerData.totalDistanceFallen;
                _gtwGameStats.TotalBigCrashes = playerData.totalBigCrashes;
                _gtwGameStats.DunceHatsCollected = playerData.dunceHatsCollected;

                _playerControl.PlayerWearableController.SetCharacterDunceHat(_gtwGameStats.DunceHatsCollected);

                Debug.Log("PlayerDataManager: _timeManager.TotalGameTime:" + _timeManager.TotalGameTime);

                success = true;
            }

            return success;
        }

        public override void Save(out object saveData)
        {
            Vector3 playerPosition = _playerControl.PlayerPosition;
            Vector3 playerForward = _playerControl.DirectionFlat;
            Vector3 velocity = _playerControl.Rigidbody.linearVelocity;
            Vector3 angularVelocity = _playerControl.Rigidbody.angularVelocity;
            int lastCheckpointId = -1;
            
            
            if (CheckpointManager.ActiveCheckpoint != null)
            {
                lastCheckpointId = CheckpointManager.CheckpointData.GetCheckpointId(CheckpointManager.ActiveCheckpoint);
                
                bool savePlayerPositionFromSave = false;
                if (_gameState.GameLevelDefinition != null)
                {
                    savePlayerPositionFromSave = _gameState.GameLevelDefinition.loadPlayerPositionFromSave;
                }
                
                if (!savePlayerPositionFromSave)
                {
                    //Save player position from checkpoint
                    playerPosition = CheckpointManager.ActiveCheckpoint.position;
                    playerForward = CheckpointManager.ActiveCheckpoint.forward;
                    velocity = Vector3.zero;
                    angularVelocity = Vector3.zero;
                }
            }
            bool giveUpAllowed = _playerControl.ragDollAbilityAvailable;

            float totalHeightGained = _gtwGameStats.TotalHeightGained;
            float totalDistanceFallen = _gtwGameStats.TotalDistanceFallen;

            int totalBigCrashes = _gtwGameStats.TotalBigCrashes;
            int totalDunceHatsCollected = _gtwGameStats.DunceHatsCollected;

            List<int> activeCheckpoints = CheckpointManager.GetSeenCheckpoints();

            PlayerSaveData playerSave = new PlayerSaveData()
            {
                position = playerPosition,
                forward = playerForward,
                velocity = velocity,
                angularVelocity = angularVelocity,
                checkpointId = lastCheckpointId,
                seenCheckpoints = activeCheckpoints,
                giveUpAbility = giveUpAllowed,
                totalGameTime = _timeManager.TotalGameTime,
                totalGameTimeUnscaled = _timeManager.TotalGameTimeUnscaled,
                totalHeightGained = totalHeightGained,
                totalDistanceFallen = totalDistanceFallen,
                totalBigCrashes = totalBigCrashes,
                dunceHatsCollected = totalDunceHatsCollected
            };

            saveData = playerSave;
        }

        public override object GetSampleSaveData()
        {
            PlayerSaveData playerSave = new PlayerSaveData();
            return playerSave;
        }

        public override System.Type[] GetSaveExtraTypes()
        {
            return null;
        }

        public override void UpgradeSave(string targetVersion, GameStateSaveData metaData, TextReader reader, out XmlSerializer serializer,
            out object saveData)
        {
            serializer = null;
            saveData = null;
        }

        public void OnDataLoadComplete()
        {

        }
    }
}