// Copyright Isto Inc.

using System;
using UnityEngine;

namespace Isto.GTW.Data
{
    [Serializable]
    public class LeaderboardEntryData
    {
        // These fields are only needed to be shown to the Editor for dummy data
        [SerializeField] private string _playerName;
        [SerializeField] private int _highestCheckpoint;

        public string PlayerName => _playerName;
        public int HighestCheckpoint => _highestCheckpoint;

        // This is specific to Steam as we are not using the Xbox leaderboard system
        public int GlobalRank { get; set; }
        public int LocalRank { get; set; }
        public Texture2D AvatarTexture   { get; set; }

        public LeaderboardEntryData()
        {
        }

        public LeaderboardEntryData(string playerName, int highestCheckpoint, int globalRank, int localRank, Texture2D avatarTexture = null)
        {
            _playerName = playerName;
            _highestCheckpoint = highestCheckpoint;
            GlobalRank = globalRank;
            LocalRank = localRank;
            AvatarTexture = avatarTexture;
        }
    }
}