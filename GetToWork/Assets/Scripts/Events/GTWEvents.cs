// Copyright Isto Inc.

namespace Isto.GTW
{
    /// <summary>
    /// This class contains the event declarations for GTW
    /// </summary>
    public static class GTWEvents
    {
        // Non-parameterized events
        public static readonly string PLAYER_FIRST_INPUT = "PlayerFirstInput";
        public static readonly string GIVE_UP_DIALOGUE_START = "GiveUpDialogueStart";
        public static readonly string GIVE_UP_ALLOWED = "GiveUpActionAllowed";
        public static readonly string DOINKLER_STAGE_START = "DoinklerStageStart";
        public static readonly string DOINKLER_STAGE_COMPLETE = "DoinklerStageComplete";
        public static readonly string DOINKLER_STAGE_RESET = "DoinklerStageReset";
        public static readonly string DOINKLER_FULL_STAGE_RESET = "DoinklerFullStageReset";

        public static readonly string DIALOGUE_EVENT = "DialogueEvent";
        public static readonly string HARD_CRASH = "HardCrashEvent";
        public static readonly string DUNCE_HAT_PICKUP = "DunceHatPickupEvent";

        // Events that send parameters
        public static readonly string FMOD_AUDIO_EVENT_COMPLETED = "FmodAudioEventCompleted";
    }
}