using Isto.Core;
using Isto.Core.Data;
using System.Collections;
using UnityEngine;
using Zenject;

namespace Isto.GTW.LevelFeatures.LevelEvents
{
    public class SaveDataEvent : LevelEvent
    {
        // UNITY HOOKUPS
        
        [SerializeField] private bool _asyncSave = true;
        
        
        // OTHER FIELDS
        
        private IGameData _gameData;
        private GTWGameState _gameState;


        // INJECTION

        [Inject]
        public void Inject(IGameData gameData, GTWGameState gameState)
        {
            _gameData = gameData;
            _gameState = gameState;
        }
        
        
        // OTHER METHODS
        
        public override void TriggerEvent(bool initialization = false)
        {
            StartCoroutine(AutoSave());
        }
        
        private IEnumerator AutoSave()
        {
            Debug.Log("ActivateCheckpointEvent.AutoSave()");

            // Notes from refactoring/debugging pass:
            // As far as I can tell this save data event is here to save the game when the user quits...
            // This is already a shaky proposition.
            // Here raising the event to display the autosave indicator just shows complete disregard for what it even
            // means to show this indicator. It's there to tell the user no to shut down the game while it's showing.
            // Yet we interrupt the game shutdown to enable the indicator and try to force a save?
            // TODO: maybe avoid this so we don't get a ton of frustrated users with broken save data as soon as we
            // launch the update??? -FG
            // TODO: if we want to try this, we don't show the indicator, we don't override the user save, and we try
            // to cleanup the state of the save data when the game next relauches. -FG
            Events.RaiseEvent(Events.GAME_AUTOSAVING); // For indicator

            int slotNumber = _gameState.SaveSlot;

            _gameState.SelectSaveGame(slotNumber);

            // TODO: I would like to understand why we need to do this?
            _gameState.LoadGameStateData(null, -1);

            _gameData.SaveGameData(slotNumber, createBackup: false, success =>
            {
                if (success)
                {
                    Events.RaiseEvent(Events.GAME_SAVED);

                    // TODO: I don't think we need this
                    _gameState.LoadSaveSlotMetaData(slotNumber, null);
                }
                else
                {
                    // TODO?
                    // The internal cause of the error should be causing an error dialog popup to happen
                    Debug.LogError($"SaveGameData for slot#{slotNumber} has failed ");
                }
            }, _asyncSave);
            
            yield return null;
        }
    }
}