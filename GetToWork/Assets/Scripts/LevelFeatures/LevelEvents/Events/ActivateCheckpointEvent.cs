// Copyright Isto Inc.

using Isto.Core;
using Isto.Core.Beings;
using Isto.Core.Data;
using Isto.Core.Game;
using Isto.GTW.Leaderboards;
using System.Collections;
using UnityEngine;
using Zenject;

#if UNITY_EDITOR
using UnityEditor;
using UnityEditor.EditorTools;
#endif

namespace Isto.GTW.LevelFeatures.LevelEvents
{
    public class ActivateCheckpointEvent : LevelEvent
    {
        [SerializeField] private Color _gizmoColor = new Color(0.5F, 0.0F, 0.65F, 0.5F);
        [SerializeField] private Checkpoint _checkpoint;
        [SerializeField] private bool _idCheck;
        [SerializeField] private float _deactivationOffsetY;

        private CheckpointData.LevelCheckpointData _checkpointData;

        // INJECTION

        private ILeaderboard _leaderboard;
        private CheckpointManager _checkpointManager;
        private BoxCollider _collider;
        private IGameData _gameData;
        private GTWGameState _gameState;
        private IPlayerController _playerController;

        private float DeactivationHeight
        {
            get
            {
                BoxCollider box = Collider;

                if (box == null)
                    return transform.position.y + _deactivationOffsetY;

                return transform.position.y + _deactivationOffsetY + box.center.y - box.size.y * 0.5f;
            }
        }

        private BoxCollider Collider
        {
            get
            {
                if (_collider == null)
                {
                    _collider = GetComponent<BoxCollider>();
                }
                return _collider;
            }
        }

        [Inject]
        public void Inject(CheckpointManager checkpointManager, IGameData gameData, GTWGameState gameState,
            IPlayerController playerController, ILeaderboard leaderboard)
        {
            _checkpointManager = checkpointManager;
            _gameData = gameData;
            _gameState = gameState;
            _playerController = playerController;
            _leaderboard = leaderboard;
        }

        protected override void Awake()
        {
            base.Awake();

            _checkpointData = _checkpoint.ToLevelCheckpointData();
        }

        private void Update()
        {
            if (GameState.CurrentlyLoading)
                return;

            if(_checkpointManager.IsCheckpointActive(_checkpointData)
            && _playerController.GetPlayerPosition().y < DeactivationHeight)
            {
                Debug.Log($"Deactivating checkpoint #{_checkpointManager.CheckpointData.GetCheckpointId(_checkpointData)}");

                _checkpointManager.SetInactiveCheckpoint(_checkpointData);
                //Auto save when a checkpoint is lost
                StartCoroutine(AutoSave());
            }
        }

        private void OnDrawGizmosSelected()
        {
            Vector3 center = new Vector3(transform.position.x, DeactivationHeight, transform.position.z);
            Vector3 size = new Vector3(10000f, 0.05f, 10000f);

            Color c = Gizmos.color;
            Gizmos.color = _gizmoColor;
            Gizmos.DrawCube(center, size);
            Gizmos.color = c;
        }

        public override void TriggerEvent(bool initialization = false)
        {
            Debug.Log($"Activating checkpoint #{_checkpointManager.CheckpointData.GetCheckpointId(_checkpointData)}");

            if (_checkpointManager.SetActiveCheckpoint(_checkpointData, _idCheck))
            {
                //Auto save when a new checkpoint is active
                StartCoroutine(AutoSave());
                if (_gameState.IsDoinklerSpecial)
                {
                    _leaderboard.UploadHighestCheckpoint(_checkpointData.checkpointId);
                }
            }
        }

        private IEnumerator AutoSave()
        {
            Debug.Log("ActivateCheckpointEvent.AutoSave()");

            Events.RaiseEvent(Events.GAME_AUTOSAVING); // For indicator

            yield return null;

            int slotNumber = _gameState.SaveSlot;

            _gameState.SelectSaveGame(slotNumber);

            // TODO: I would like to understand why we need to do this?
            _gameState.LoadGameStateData(null, -1);

            _gameData.SaveGameData(slotNumber, createBackup: false, success =>
            {
                if (success)
                {
                    Events.RaiseEvent(Events.GAME_SAVED);

                    // TODO: I don't think we need this
                    _gameState.LoadSaveSlotMetaData(slotNumber, null);
                }
                else
                {
                    // TODO?
                    // The internal cause of the error should be causing an error dialog popup to happen
                    Debug.LogError($"SaveGameData for slot#{slotNumber} has failed ");
                }
            });
        }

        [ContextMenu("Set to Tower Checkpoint")]
        private void SetToTowerLevelCheckpoint()
        {
            _idCheck = true;
            _deactivationOffsetY = 0f;

#if UNITY_EDITOR
            EditorUtility.SetDirty(gameObject);
#endif
        }

        [ContextMenu("Set to Challenger Checkpoint")]
        private void SetToChallengerCheckpoint()
        {
            _idCheck = true;
            _deactivationOffsetY = float.MinValue;

#if UNITY_EDITOR
            EditorUtility.SetDirty(gameObject);
#endif
        }

        [ContextMenu("Set to Challenger Tower Checkpoint")]
        private void SetToChallengerTowerCheckpoint()
        {
            _idCheck = false;
            _deactivationOffsetY = float.MinValue;

#if UNITY_EDITOR
            EditorUtility.SetDirty(gameObject);
#endif
        }
    }
}