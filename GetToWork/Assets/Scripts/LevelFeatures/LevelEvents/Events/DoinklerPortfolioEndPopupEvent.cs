// Copyright Isto Inc.

using PixelCrushers.DialogueSystem;
using UnityEngine;


namespace Isto.GTW.LevelFeatures.LevelEvents
{
    public class DoinklerPortfolioEndPopupEvent : LevelTransitionPopupEvent
    {

        // OTHER FIELDS

        private Coroutine _levelCompletedCoroutine = null;
        [SerializeField] private Animator _endSceneAnimator;


        // EVENT HANDLING

        protected override void ModalPopup_OnPlayerRespawn()
        {
            base.ModalPopup_OnPlayerRespawn();
            // Resetting the level to its original state
            _levelEventTrigger.wasTriggered = false;
            _endSceneAnimator.gameObject.SetActive(false);
            DialogueManager.StopConversation();
            _sounds.StopAllEventInstances();
        }
    }
}