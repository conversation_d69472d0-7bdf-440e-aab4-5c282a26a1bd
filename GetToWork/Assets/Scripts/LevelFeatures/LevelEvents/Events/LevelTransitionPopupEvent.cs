// Copyright Isto Inc.

using Isto.Core;
using Isto.Core.Audio;
using Isto.Core.Data;
using System.Collections;
using Isto.Core.UI;
using Isto.GTW.Audio;
using Isto.GTW.Configuration;
using Isto.GTW.Player;
using Isto.GTW.UI;
using UnityEngine;
using Zenject;

namespace Isto.GTW.LevelFeatures.LevelEvents
{
    public class LevelTransitionPopupEvent : LevelEvent
    {
        // UNITY HOOKUP

        [SerializeField] private GTWGameLevelDefinition _currentLevel;
        [SerializeField] private GTWGameLevelDefinition _nextLevel; // This could be changed to a checkpoint perhaps?

        [SerializeField] private GameObject _cashParticleSystem;
        [SerializeField] private GameObject _glassCapsule;

        [SerializeField] private float _popupDelay = 3f;

        public GTWGameLevelDefinition CurrentLevel => _currentLevel;

        // INJECTION

        private GTWUINextLevelPopup _nextLevelPopup;
        private PlayerController _playerController;
        private SimpleGameMenuStateMachine _gameMenuStateMachine;
        private GTWGameState _gameState;
        private IGameData _gameData;
        protected GTWGameSounds _sounds;


        // OTHER FIELDS

        private Coroutine _levelCompletedCoroutine = null;


        [Inject]
        public void Inject(GTWUINextLevelPopup nextLevelPopup, SimpleGameMenuStateMachine gameMenuStateMachine,
            PlayerController playerController, GTWGameState gameState, IGameData gameData, IGameSounds sounds)
        {
            _gameMenuStateMachine = gameMenuStateMachine;
            _nextLevelPopup = nextLevelPopup;
            _playerController = playerController;
            _gameState = gameState;
            _gameData = gameData;
            _sounds = sounds as GTWGameSounds;
        }


        // OTHER METHODS

        public override void TriggerEvent(bool initialization = false)
        {
            if (IsTriggerEventValid && !_nextLevelPopup.IsOpen)
            {
                Events.RaiseEvent(GTWEvents.DOINKLER_STAGE_COMPLETE);
                _levelCompletedCoroutine = StartCoroutine(LevelCompleted());
            }
        }


        private IEnumerator LevelCompleted()
        {
            _currentLevel.IsCompleted = true;
            if (_levelCompletedCoroutine != null)
            {
                StopCoroutine(_levelCompletedCoroutine);
                _levelCompletedCoroutine = null;
            }

            int slotNumber = _gameState.SaveSlot;//Save slot should have been set either in UISetGameModeSubState or GTWUINextLevelPopup

            _gameState.SelectSaveGame(slotNumber);

            // TODO: I would like to understand why we need to do this?
            _gameState.LoadGameStateData(null, -1);

            _gameData.SaveGameData(slotNumber, createBackup: false, success =>
            {
                if (success)
                {
                    Events.RaiseEvent(Events.GAME_SAVED);

                    // TODO: I don't think we need this
                    _gameState.LoadSaveSlotMetaData(slotNumber, null);
                }
                else
                {
                    // TODO?
                    // The internal cause of the error should be causing an error dialog popup to happen
                    Debug.LogError($"SaveGameData for slot#{slotNumber} has failed ");
                }
            });

            //NOTE: the gameobject containing the particle system is set to auto-disable (See "Stop Action" on the particle system component)
            // at the end of its lifecycle (approx 10 seconds) for optimization purposes
            _cashParticleSystem.SetActive(true);
            _glassCapsule.SetActive(false);

            //Freeze player controls
            _playerController._freezeControlsState.ForceBrake = true;
            _playerController._freezeControlsState.ForceGrabAbility = false;
            _playerController.ChangeToFreezeControlsState();

            yield return new WaitForSeconds(_popupDelay);

            //Restore player controls
            _playerController.ChangeToMoveState();
            _cashParticleSystem.SetActive(false);
            _glassCapsule.SetActive(true);
            _nextLevelPopup.SetLevelDefinitionData(_currentLevel, _nextLevel, ModalPopup_OnPlayerRespawn);
            _gameMenuStateMachine.EnterSubState(_nextLevelPopup);

            yield return null;

            _levelCompletedCoroutine = null;
        }

        // EVENT HANDLING

        protected virtual void ModalPopup_OnPlayerRespawn()
        {
            SetTriggered(false);
        }
    }
}