// Copyright Isto Inc.

using Isto.Core;
using Isto.GTW.Dialogues;
using Isto.GTW.LevelFeatures;
using PixelCrushers.DialogueSystem;
using UnityEngine;
using Zenject;
using CharacterInfo = PixelCrushers.DialogueSystem.CharacterInfo;
using PixelCrusherTools = PixelCrushers.DialogueSystem.Tools;


namespace Isto.GTW.LevelEvents.LevelEvents
{
    public class DialogueEvent : LevelEvent
    {
        // UNITY HOOKUPS

        public DialogueDatabase selectedDatabase;

        [ConversationPopup]
        public string conversation;

        [Tooltip("Queue the conversation if another conversation is playing. Uncheck to not play the conversation if another is currently playing or in queue.")]
        [SerializeField] private bool _queueConversation = true;

        [SerializeField] private Transform _conversationActor;
        [SerializeField] private Transform _conversationConversant;


        // PROPERTIES

        public string UniqueID => conversation;


        // INJECTION

        private GTWGameState _gtwGameState;

        [Inject]
        public void Inject(GTWGameState gameState)
        {
            _gtwGameState = gameState;
        }

        // OTHER METHODS

        public override void TriggerEvent(bool initialization = false)
        {
            if (!initialization)
            {
                StartConversation();
            }
        }

        protected void StartConversation()
        {
            if (!_queueConversation && ConversationQueueManager.instance.IsConversationPlayingOrWaitingToPlay)
            {
                return;
            }

            var actorTransform = PixelCrusherTools.Select(_conversationActor, null);
            var conversantTransform = _conversationConversant;
            if (conversantTransform == null)
            {
                Conversation conversationAsset = DialogueManager.MasterDatabase.GetConversation(conversation);
                Actor conversationConversantActor = DialogueManager.MasterDatabase.GetActor(conversationAsset.ConversantID);
                Transform registeredTransform = conversationConversantActor != null ? CharacterInfo.GetRegisteredActorTransform(conversationConversantActor.Name) : null;
                conversantTransform = registeredTransform != null ? registeredTransform : transform;
            }
            ConversationQueueManager.instance.AddConversation(conversation, actorTransform, conversantTransform);

            // This event is specific to the main game. This check is in place to ensure that the Podcasts don't
            // get added to the other Save Slots that are in use for the DoinklerPortfolio levels
            if (!_gtwGameState.IsDoinklerPortfolio && !_gtwGameState.IsDoinklerSpecial)
            {
                Events.RaiseEvent(GTWEvents.DIALOGUE_EVENT);
            }
        }
    }
}