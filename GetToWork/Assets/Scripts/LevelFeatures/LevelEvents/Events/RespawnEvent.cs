// Copyright Isto Inc.

using Isto.Core;
using Isto.GTW.Player;
using UnityEngine;
using Zenject;

namespace Isto.GTW.LevelFeatures.LevelEvents
{
    public class RespawnEvent : LevelEvent
    {
        public enum RestartType
        {
            NORMAL,
            DOINKLER_STAGE_RESET,
            DOINKLER_FULL_RESET
        }
        
        // UNITY HOOKUP

        [SerializeField] private bool _showBlackOut = true;
        [SerializeField] private bool _restartAtFirstCheckpoint = false;
        [SerializeField] private RestartType _restartType = RestartType.NORMAL;
        
        
        // INJECTION

        private PlayerController _playerController;
        private CheckpointManager _checkpointManager;

        [Inject]
        public void Inject(PlayerController playerController, CheckpointManager checkpointManager)
        {
            playerController = _playerController;
            _checkpointManager = checkpointManager;
        }

        public override void TriggerEvent(bool initialization = false)
        {
            if (_restartAtFirstCheckpoint)
            {
                _checkpointManager.SetActiveCheckpoint(_checkpointManager.StartingCheckpoint, false);
            }

            switch (_restartType)
            {
                case RestartType.DOINKLER_STAGE_RESET:
                    Events.RaiseEvent(GTWEvents.DOINKLER_STAGE_RESET, true);
                    break;
                case RestartType.DOINKLER_FULL_RESET:
                    Events.RaiseEvent(GTWEvents.DOINKLER_FULL_STAGE_RESET, true);
                    break;
                case RestartType.NORMAL:
                    _checkpointManager.Respawn(_showBlackOut);
                    break;
            }
        }
    }
}