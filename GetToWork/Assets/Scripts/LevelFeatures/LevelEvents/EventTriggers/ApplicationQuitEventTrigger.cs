using System;
using UnityEngine;

namespace Isto.GTW.LevelFeatures.LevelEventTriggers
{
    public class ApplicationQuitEventTrigger : LevelEventTrigger
    {
        // UNITY HOOKUPS
        
        [SerializeField] private bool _triggerOnApplicationQuit = true;
        [SerializeField] private bool _triggerOnLoseFocus = false;
        [SerializeField] private bool _triggerOnFocus = false;
        
        
        // OTHER METHODS
        
        private void OnApplicationQuit()
        {
            if (_triggerOnApplicationQuit)
            {
                TriggerEvent();
            }
        }

        private void OnApplicationFocus(bool hasFocus)
        {
            if (hasFocus && _triggerOnFocus)
            {
                TriggerEvent();
            }
            else if (!hasFocus && _triggerOnLoseFocus)
            {
                TriggerEvent();
            }
        }
    }
}