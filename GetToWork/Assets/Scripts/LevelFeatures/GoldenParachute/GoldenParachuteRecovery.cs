// Copyright Isto Inc.

using Isto.Core.Beings;
using ModestTree;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Zenject;

namespace Isto.GTW.LevelFeatures.GoldenParachute
{
    /// <summary>
    /// This class is an intial implementation of the Golden Parachute flow. If the users make it past the CEO section
    /// and fall below the CEO office, they will respawn at the door leading to the bonus ending.
    /// </summary>
    public class GoldenParachuteRecovery : LevelEventTrigger
    {
        // UNITY HOOKUP

        [SerializeField] private Color _goldenParachuteGizmoColor = new Color(0.5F, 0.0F, 0.65F, 0.5F);
        [SerializeField] private float _deactivationHeight;
        [SerializeField] private string _checkpointName;
        [SerializeField] private string _sceneName;


        // OTHER FIELDS

        private bool _isInSpaceSection = false;


        // INJECTION

        private CheckpointManager _checkpointManager;
        private BoxCollider _collider;
        private IPlayerController _playerController;


        [Inject]
        public void Inject(CheckpointManager checkpointManager, IPlayerController playerController)
        {
            _checkpointManager = checkpointManager;
            _playerController = playerController;
        }


        // UNITY LIFECYCLE

        private void Update()
        {
            int checkpointId = _checkpointManager.CheckpointData.GetCheckpointId(_checkpointName, _sceneName);
            List<int> seenCheckpoints = _checkpointManager.GetSeenCheckpoints();

            if (!seenCheckpoints.IsEmpty() && !_isInSpaceSection)
            {
                if (seenCheckpoints.Last() >= checkpointId)
                {
                    _isInSpaceSection = true;
                }
            }

            if (_isInSpaceSection && _playerController.GetPlayerPosition().y < _deactivationHeight)
            {
                _checkpointManager.SetActiveCheckpoint(_checkpointManager.CheckpointData.levelCheckpointDatas[checkpointId]);
                _checkpointManager.Respawn(showBlackOut: false);
                TriggerEvent();
            }
        }

        private void OnDrawGizmosSelected()
        {
            Vector3 center = new Vector3(transform.position.x, _deactivationHeight, transform.position.z);
            Vector3 size = new Vector3(10000f, 0.05f, 10000f);

            Color c = Gizmos.color;
            Gizmos.color = _goldenParachuteGizmoColor;
            Gizmos.DrawCube(center, size);
            Gizmos.color = c;
        }
    }
}