using System;
using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace Isto.GTW.LevelFeatures
{
    public class CheckpointWindow : EditorWindow
    {
        // OTHER FIELDS
        
        private static List<CheckpointData.LevelCheckpointData> _levelCheckpointDatas = new List<CheckpointData.LevelCheckpointData>();
        private static List<Checkpoint> _sceneCheckpoints = new List<Checkpoint>();
        private Vector2 _scrollPos;
    
        private string _searchString = "";
        private static List<CheckpointData.LevelCheckpointData> _searchCheckpoints = new List<CheckpointData.LevelCheckpointData>();
        
        private bool focused = false;

        private CheckpointManager _checkpointManager = null;


        // PROPERTIES
        
        public CheckpointManager CheckpointManager
        {
            get
            {
                if (_checkpointManager == null)
                {
                    _checkpointManager = FindObjectOfType<CheckpointManager>();
                }

                return _checkpointManager;
            }
        }
        
        
        // OTHER METHODS
        
        [MenuItem("GTW/Checkpoints")]
        public static void OpenWindow()
        {
            var window = GetWindow<CheckpointWindow>();
            window.titleContent = new GUIContent("Checkpoints");
            window.Show();
        }
        
        private void OnEnable()
        {
            RefreshCheckpoints();
            
            EditorApplication.playModeStateChanged -= OnPlayStateModeChange;
            EditorApplication.playModeStateChanged += OnPlayStateModeChange;
        }

        private void OnDisable()
        {
            EditorApplication.playModeStateChanged -= OnPlayStateModeChange;
        }

        private void OnFocus()
        {
            RefreshCheckpoints();
        }

        private void OnPlayStateModeChange(PlayModeStateChange playModeStateChange)
        {
            RefreshCheckpoints();
        }

        private void RefreshCheckpoints()
        {
            _levelCheckpointDatas.Clear();
            _sceneCheckpoints.Clear();

            CheckpointManager checkpointManager = CheckpointManager;
            if (checkpointManager != null && checkpointManager.CheckpointData != null)
            {
                _levelCheckpointDatas = checkpointManager.CheckpointData.levelCheckpointDatas.ToList();
                _levelCheckpointDatas = _levelCheckpointDatas.OrderBy(x => x.FullName).ToList();
                _sceneCheckpoints = FindObjectsOfType<Checkpoint>().ToList();
            }

            RefreshSearch(_searchString);
        }

        private void RefreshSearch(string searchTerm)
        {
            searchTerm = searchTerm.ToLower();
            _searchCheckpoints.Clear();
            bool isSearchEmpty = string.IsNullOrEmpty(searchTerm);
        
            foreach (CheckpointData.LevelCheckpointData checkpoint in _levelCheckpointDatas)
            {
                if (isSearchEmpty || checkpoint.FullName.ToLower().Contains(searchTerm))
                {
                    _searchCheckpoints.Add(checkpoint);
                }
            }
        }

        private Checkpoint GetSceneCheckpoint(CheckpointData.LevelCheckpointData checkpointData)
        {
            foreach (Checkpoint checkpoint in _sceneCheckpoints)
            {
                if (checkpoint == null || string.IsNullOrEmpty(checkpoint.SceneName) || string.IsNullOrEmpty(checkpoint.CheckpointName))
                {
                    continue;
                }
                
                if (checkpoint.SceneName == checkpointData.sceneName && checkpoint.CheckpointName == checkpointData.checkpointName)
                {
                    return checkpoint;
                }
            }

            return null;
        }
        
        private static void SelectCheckpoint(Checkpoint checkpoint)
        {
            Selection.activeObject = checkpoint.gameObject;
        }
        
        private void OnGUI()
        {
            _scrollPos = EditorGUILayout.BeginScrollView(_scrollPos, GUILayout.ExpandWidth(true), GUILayout.ExpandHeight(true));
        
            // Style
            Color defaultColor = GUI.color;
            GUIStyle buttonStyle = EditorStyles.miniButton;
            buttonStyle.alignment = TextAnchor.MiddleLeft;
            buttonStyle.stretchWidth = true;

            // Checkpoints
            EditorGUILayout.Space(5);
            
            if (CheckpointManager == null || CheckpointManager.CheckpointData == null)
            {
                EditorGUILayout.LabelField("No checkpoint manager found or CheckpointData is null");
                EditorGUILayout.EndScrollView();
                return;
            }


            
            GUI.color = new Color(0f, 1f, 1f);
            if (GUILayout.Button("Update Checkpoint Data", GUILayout.Height(30f)))
            {
                CheckpointDataEditor.UpdateCheckpointList(CheckpointManager.CheckpointData);
                RefreshCheckpoints();
            }
            GUI.color = defaultColor;
            
            EditorGUILayout.Space(5);
            EditorGUILayout.HelpBox(
                "[Teal] Click after editing/moving/adding checkpoints." +
                "\nPress CTRL+S to save the changes afterward.",
                MessageType.Info);
            EditorGUILayout.Space(5);
            
            if (GUILayout.Button("Select Checkpoint Data", GUILayout.Height(30f)))
            {
                Selection.activeObject = CheckpointManager.CheckpointData;
            }
            EditorGUILayout.Space(5);

            if (_levelCheckpointDatas == null || _levelCheckpointDatas.Count == 0)
            {
                EditorGUILayout.LabelField("No checkpoints found");
                EditorGUILayout.EndScrollView();
                return;
            }
           
            EditorGUILayout.LabelField("Checkpoints");

            GUI.SetNextControlName("CheckpointsSearch");
            string newSearchString = GUILayout.TextField(_searchString, GUI.skin.FindStyle("ToolbarSearchTextField"));
            if (!focused)
            {
                EditorGUI.FocusTextInControl("CheckpointsSearch");
                focused = true;
            }
            if (newSearchString != _searchString)
            {
                _searchString = newSearchString;
                RefreshSearch(newSearchString);
            }

            int chkId = 0;

            foreach (CheckpointData.LevelCheckpointData checkpoint in _searchCheckpoints)
            {
                if (checkpoint == null)
                {
                    continue;
                }
                
                EditorGUILayout.BeginHorizontal(GUILayout.ExpandWidth(true));
                
                GUI.enabled = EditorApplication.isPlaying;
                if (GUILayout.Button(chkId.ToString() + " - " + checkpoint.sceneName + " " + checkpoint.checkpointName, buttonStyle, GUILayout.ExpandWidth(true)))
                {
                    CheckpointManager.SetActiveCheckpoint(checkpoint);
                    CheckpointManager.Respawn(showBlackOut: false);
                }
                
                GUI.enabled = !EditorApplication.isPlaying;
                int checkpointId = CheckpointManager.CheckpointData.levelCheckpointDatas.IndexOf(checkpoint);
                if (checkpointId == CheckpointManager.StartingCheckpointId)
                {
                    GUI.color = Color.cyan;
                }
                if (GUILayout.Button("Set Start", buttonStyle, GUILayout.Width(65f)))
                {
                    SetStartingCheckPoint(checkpointId);
                }
                GUI.color = defaultColor;

                Checkpoint sceneCheckpoint = GetSceneCheckpoint(checkpoint);
                GUI.enabled = sceneCheckpoint != null;
                if (GUILayout.Button("Select", buttonStyle, GUILayout.Width(50f)))
                {
                    SelectCheckpoint(sceneCheckpoint);
                }
                GUI.enabled = true;
                EditorGUILayout.EndHorizontal();

                chkId++;
            }
        
            EditorGUILayout.EndScrollView();
        }

        private void SetStartingCheckPoint(int checkpointId)
        {
            CheckpointManager.CheckpointData.SetSpawnAtStartId(checkpointId);
            EditorUtility.SetDirty(CheckpointManager.CheckpointData);
        }
    }
}