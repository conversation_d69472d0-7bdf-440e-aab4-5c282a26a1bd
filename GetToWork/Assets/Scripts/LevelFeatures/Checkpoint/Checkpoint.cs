using System;
using System.Collections.Generic;
using Isto.GTW.DebugTools;
using Isto.GTW.Player;
using UnityEngine;
using Zenject;

namespace Isto.GTW.LevelFeatures
{
    public class Checkpoint : MonoBehaviour
    {
        public enum CheckpointIconState { NEVER_SEEN, ALREADY_REACHED, CURRENT, PREVIOUS }

        // UNITY HOOKUP

        [Tooltip("Forward (Z) axis used for spawn rotation.")]
        [SerializeField] private Transform _spawnPosition = null;
        [SerializeField] private bool _spawnOnGround = true;
        [SerializeField] private bool _showInSpeedrunTimer = true;

        [Header("State Visuals")]
        [SerializeField] private GameObject _neverReached = null;
        [SerializeField] private GameObject _reachedBefore = null;
        [SerializeField] private GameObject _current = null;
        [SerializeField] private GameObject _previous = null;
        [Space]
        [SerializeField] private GameObject _furthest = null;
        [Head<PERSON>("State Transitions")]
        [SerializeField] private GameObject _neverReachedToCurrent = null;
        [SerializeField] private GameObject _reachedBeforeOrPreviousToCurrent = null;
        
        


        // OTHER FIELDS

        private CheckpointIconState _iconState = CheckpointIconState.NEVER_SEEN;


        // PROPERTIES

        public string CheckpointName => gameObject.name;
        public int CheckpointId { get; private set; } = -1;
        public string SceneName => gameObject.scene.name;
        public CheckpointManager CheckpointManager => _checkpointManager;



        // INJECTION

        private CheckpointManager _checkpointManager;
        private PlayerController _playerController;

        [Inject]
        public void Inject(CheckpointManager checkpointManager, PlayerController playerController)
        {
            _checkpointManager = checkpointManager;
            _playerController = playerController;
        }


        // LYFECYCLE EVENTS

        private void Awake()
        {
            CheckpointId = CheckpointManager.CheckpointData.GetCheckpointId(this);
            CheckpointManager.OnUpdateAllCheckpointIcons += UpdateCheckpointState;
            UpdateCheckpointState();
        }

        private void OnDestroy()
        {
            CheckpointManager.OnUpdateAllCheckpointIcons -= UpdateCheckpointState;
        }

        private void Reset()
        {
            _spawnPosition = this.transform;
        }

        void OnDrawGizmos()
        {
            Gizmos.DrawIcon(transform.position, "Checkpoint", true, Color.red);
        }

        private void OnDrawGizmosSelected()
        {
            DebugExtensions.DrawArrow(transform.position, 0.3f, transform.forward, Color.red);
        }


        // OTHER METHODS

        public CheckpointData.LevelCheckpointData ToLevelCheckpointData()
        {
            return new CheckpointData.LevelCheckpointData()
            {
                checkpointName = CheckpointName,
                sceneName = SceneName,
                checkpointId = CheckpointId,
                position = _spawnPosition.position,
                forward = _spawnPosition.forward,
                spawnOnGround = _spawnOnGround,
                showInSpeedrunTimer = _showInSpeedrunTimer
            };
        }

        public void UpdateCheckpointState()
        {
            if(CheckpointManager.ActiveCheckpoint == null)
                return;

            CheckpointIconState newState = CheckpointIconState.CURRENT;
            
            if (CheckpointId == CheckpointManager.ActiveCheckpoint.checkpointId)
            {
                newState = CheckpointIconState.CURRENT;
            }
            else if (CheckpointManager.HasSeenCheckpoint(CheckpointId))
            {
                if (CheckpointId > CheckpointManager.ActiveCheckpoint.checkpointId)
                {
                    newState = CheckpointIconState.ALREADY_REACHED;
                }
                else
                {
                    newState = CheckpointIconState.PREVIOUS;
                }
            }
            else
            {
                newState = CheckpointIconState.NEVER_SEEN;
            }

            UpdateCheckpointState(newState);
        }


        private void UpdateCheckpointState(CheckpointIconState newState)
        {
            CheckpointIconState oldState = _iconState;
            _iconState = newState;
            
            //State transitions
            if (_reachedBeforeOrPreviousToCurrent != null)
            {
                _reachedBeforeOrPreviousToCurrent.SetActive(false); //Reset since this transition can happen many times in one playthrough
            }

            if (oldState == CheckpointIconState.NEVER_SEEN
                && newState == CheckpointIconState.CURRENT)
            {
                NeverReachedToCurrent();
            }
            else if ((oldState == CheckpointIconState.ALREADY_REACHED || oldState == CheckpointIconState.PREVIOUS)
                && newState == CheckpointIconState.CURRENT)
            {
                AlreadyReachedOrPreviousToCurrent();
            }

            // Icon Visuals
            if (_neverReached != null) _neverReached.SetActive(_iconState == CheckpointIconState.NEVER_SEEN);
            if (_reachedBefore != null) _reachedBefore.SetActive(_iconState == CheckpointIconState.ALREADY_REACHED);
            if (_current != null) _current.SetActive(_iconState == CheckpointIconState.CURRENT);
            if (_previous != null) _previous.SetActive(_iconState == CheckpointIconState.PREVIOUS);

            List<int> seenCheckpoints = CheckpointManager.GetSeenCheckpoints();
            if (_furthest != null) _furthest.SetActive(seenCheckpoints.Count > 0 && CheckpointManager.GetSeenCheckpoints()[^1] == CheckpointId);//Check if id matches last checkpoint id in seen list
        }

        private void AlreadyReachedOrPreviousToCurrent()
        {
            if (_reachedBeforeOrPreviousToCurrent != null)
            {
                _reachedBeforeOrPreviousToCurrent.SetActive(true);
            }
        }

        private void NeverReachedToCurrent() {
            if ( _neverReachedToCurrent != null ) {
                _neverReachedToCurrent.SetActive(true);
            }
        }

    }
}