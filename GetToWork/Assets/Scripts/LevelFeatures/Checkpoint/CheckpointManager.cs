// Copyright Isto Inc.
using System;
using Isto.Core.Game;
using Isto.GTW.Camera;
using Isto.GTW.Configuration;
using Isto.GTW.Player;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Isto.Core.UI;
using UnityEngine;
using Zenject;

namespace Isto.GTW.LevelFeatures
{
    public class CheckpointManager : MonoBehaviour
    {
        // UNITY HOOKUP

        [SerializeField] private CheckpointData _checkpointData = null;


        // OTHER FIELDS

        private CheckpointData.LevelCheckpointData _activeCheckpoint = null;

        private PlayerController _playerController;
        private Vector3 _defaultPlayerPosition = Vector3.zero;
        private Quaternion _defaultPlayerRotation = Quaternion.identity;

        private List<int> _seenCheckpointIds = new List<int>();

        private CanvasGroup _sceneBlackOutCanvas = null;
        private Coroutine _HideBlackOutAnimationCoroutine = null;

        // PROPERTIES

        public CheckpointData CheckpointData => _checkpointData;
        public CheckpointData.LevelCheckpointData StartingCheckpoint => CheckpointData.StartingCheckpoint;
        public int StartingCheckpointId => CheckpointData.startingCheckpointId;

        public PlayerController PlayerController
        {
            get
            {
                return _playerController;
            }
            set
            {
                _playerController = value;
                _defaultPlayerPosition = _playerController.PlayerTransform.position;
                _defaultPlayerRotation = _playerController.PlayerTransform.rotation;
            }
        }

        public PlayerVirtualCameraController PlayerVirtualCameraController => PlayerController.PlayerVirtualCameraController;


        public CheckpointData.LevelCheckpointData ActiveCheckpoint => _activeCheckpoint;


        // EVENTS

        public event Action OnUpdateAllCheckpointIcons;


        // INJECTION

        private GTWGameState _gameState;
        private UIAccessManager _uiAccessManager;

        [Inject]
        public void Inject(GTWGameState gameState, PlayerController playerController, UIAccessManager uiAccessManager)
        {
            _gameState = gameState;
            PlayerController = playerController;
            PlayerController.CheckpointManager = this;
            _uiAccessManager = uiAccessManager;
        }


        // LIFECYCLE EVENTS

        private void Start()
        {
            _sceneBlackOutCanvas = _uiAccessManager.GetSingleCanvasGroup(UIAccessManager.CanvasGroupId.SceneBlackOut);

            SetActiveCheckpoint(CheckpointData.StartingCheckpoint);

            if (GameState.GameModeLoadedFromGameState)
            {
                if (_gameState.CurrentGameMode != null && _gameState.GameLevelDefinition != null)
                {
                    //Load checkpoint specified in GameMode
                    GTWGameLevelDefinition gtwGameLevel = _gameState.GameLevelDefinition;

                    if (!_gameState.StartedWithoutGameMode && gtwGameLevel.CheckpointNumber > 0 &&
                        gtwGameLevel.CheckpointNumber < CheckpointData.levelCheckpointDatas.Count)
                    {
                        CheckpointData.LevelCheckpointData checkpoint =
                            CheckpointData.levelCheckpointDatas[gtwGameLevel.CheckpointNumber];
                        SetActiveCheckpoint(checkpoint, idCheck: false);
                    }
                }
            }

            StartCoroutine(RespawnPlayerWhenDoneLoading());

            OnUpdateAllCheckpointIcons?.Invoke();
        }


        // OTHER METHODS

        IEnumerator RespawnPlayerWhenDoneLoading()
        {
            PlayerController.ChangeToWaitForSectionLoadingState(); // When we start the game, ensure that the player is waiting for section to load

            while (GameState.CurrentlyLoading)
            {
                yield return null;
            }
            
            bool respawnPlayer = true;
            
            if (GTWGameState.LoadingFromSave)
            {
                if (_gameState.GameLevelDefinition != null && _gameState.GameLevelDefinition.loadPlayerPositionFromSave)
                {
                    respawnPlayer = false;
                }
            }

            if (respawnPlayer)
            {
                Respawn(showBlackOut: false);
            }
        }

        public bool SetActiveCheckpoint(CheckpointData.LevelCheckpointData checkpoint, bool idCheck = false)
        {
            int checkpointId = _checkpointData.GetCheckpointId(checkpoint);
            checkpoint.checkpointId = checkpointId;

            if (!_seenCheckpointIds.Contains(checkpointId))
            {
                _seenCheckpointIds.Add(checkpointId);
                _seenCheckpointIds.Sort();
            }

            if (idCheck && _activeCheckpoint != null)
            {
                if (checkpointId > _checkpointData.GetCheckpointId(_activeCheckpoint))
                {
                    _activeCheckpoint = checkpoint;
                    OnUpdateAllCheckpointIcons?.Invoke();

                    return true;
                }
            }
            else
            {
                _activeCheckpoint = checkpoint;
                OnUpdateAllCheckpointIcons?.Invoke();

                return true;
            }

            OnUpdateAllCheckpointIcons?.Invoke();

            return false;
        }

        public bool SetInactiveCheckpoint(CheckpointData.LevelCheckpointData checkpoint)
        {
            int checkpointId = _checkpointData.GetCheckpointId(checkpoint);

            bool result = _seenCheckpointIds.Remove(checkpointId);

            if (_activeCheckpoint != null
             && checkpointId == _checkpointData.GetCheckpointId(_activeCheckpoint))
            {
                int activeCheckpointId;
                if (_seenCheckpointIds.Count > 0)
                {
                    activeCheckpointId = _seenCheckpointIds.Last();
                }
                else
                {
                    activeCheckpointId = 0;
                }

                _activeCheckpoint = _checkpointData.levelCheckpointDatas[activeCheckpointId];
                OnUpdateAllCheckpointIcons?.Invoke();
            }

            return result;
        }

        public bool IsCheckpointActive(CheckpointData.LevelCheckpointData checkpoint)
        {
            int checkpointId = _checkpointData.GetCheckpointId(checkpoint);
            return _seenCheckpointIds.Contains(checkpointId);
        }

        public List<int> GetSeenCheckpoints()
        {
            return _seenCheckpointIds;
        }

        public bool HasSeenCheckpoint(int id)
        {
            return _seenCheckpointIds.Contains(id);
        }

        public void Respawn(bool showBlackOut = false)
        {
            if (showBlackOut)
            {
                ShowBlackOut();
            }
            
            if (ActiveCheckpoint != null)
            {
                Debug.Log("Respawning Player at active checkpoint");
                PlayerController.RespawnAtCheckpoint(ActiveCheckpoint);
            }
            else if (RespawnAtStartingCheckpoint())
            {

            }
            else
            {
                RespawnAtDefaultPosition();
            }

            if (showBlackOut)
            {
                HideBlackOut();
            }
        }

        public bool RespawnAtStartingCheckpoint()
        {
            if (StartingCheckpoint != null)
            {
                Debug.Log("Respawning Player at starting checkpoint");
                SetActiveCheckpoint(StartingCheckpoint);
                PlayerController.RespawnAtCheckpoint(ActiveCheckpoint);

                return true;
            }

            return false;
        }

        public bool RespawnAtDefaultPosition()
        {
            Debug.Log("Respawning Player at default position");
            PlayerController.Respawn();
            PlayerController.TeleportCharacter(_defaultPlayerPosition, _defaultPlayerRotation);
            PlayerController.ResetVelocityToZero();
            PlayerVirtualCameraController.UpdateCamera(true);
            _playerController.ChangeToWaitForSectionLoadingState();

            return true;
        }

        private void ShowBlackOut()
        {
            _sceneBlackOutCanvas.gameObject.SetActive(true);
            _sceneBlackOutCanvas.alpha = 1f;
        }

        private void HideBlackOut()
        {
            _HideBlackOutAnimationCoroutine = StartCoroutine(HideBlackOutAnimation());
        }

        private IEnumerator HideBlackOutAnimation()
        {
            if (_HideBlackOutAnimationCoroutine != null)
            {
                StopCoroutine(_HideBlackOutAnimationCoroutine);
                _HideBlackOutAnimationCoroutine = null;
            }

            yield return new WaitForSeconds(0.1f);

            float alpha = _sceneBlackOutCanvas.alpha;
            float cameraHeightAnim = alpha;
            while (alpha > 0f)
            {
                alpha -= Time.deltaTime;
                cameraHeightAnim = Mathf.InverseLerp(0.5f, 1f, alpha);
                _sceneBlackOutCanvas.alpha = Mathf.Max(0f, alpha);
                PlayerVirtualCameraController.AnimatedCameraPositionOffset = new Vector3(0f, cameraHeightAnim * 0.2f, 0f);
                yield return null;
            }

            _sceneBlackOutCanvas.gameObject.SetActive(false);
            PlayerVirtualCameraController.AnimatedCameraPositionOffset = Vector3.zero;

            _HideBlackOutAnimationCoroutine = null;
        }
    }
}