// Copyright Isto Inc.

using Isto.Core;
using Isto.Core.Beings;
using Isto.Core.Data;
using Isto.Core.Localization;
using Isto.Core.Speedrun;
using Isto.GTW.Configuration;
using Isto.GTW.LevelFeatures;
using Isto.GTW.Providers;
using Rewired.Data;
using System;
using System.Collections;
using System.Linq;
using TMPro;
using UnityEngine;
using Zenject;

namespace Isto.GTW.Speedrun
{
    public class DoinklerSpecialSpeedrunTimer : SpeedrunTimer
    {

        // UNITY HOOKUP

        [Header("Doinkler Hookups")]
        [SerializeField] private TextMeshProUGUI _fileNameHeader;
        [SerializeField] private TextMeshProUGUI _currentCheckpointProgress;
        [SerializeField] private TextMeshProUGUI _currentHeightProgress;
        [SerializeField] private GTWGameLevelDefinition _doinklerSpecialDefinition;


        // OTHER FIELDS

        private bool _isTimeOverride = false;

        private bool _init = false;
        private CheckpointManager _checkpointManager;
        private int _maxCheckpoint = 1;

        // INJECTION

        IPlayerController _player;
        private IDoinklerWorldDefinitionProvider _doinklerWorldDefinitionProvider;

        private GTWGameState _gtwGameState;
        private LocTerm.Factory _localizedStringFactory;
        private GTWGameStats _gtwGameStats;

        [Inject]
        public void Inject(IPlayerController player, IDoinklerWorldDefinitionProvider doinklerWorldDefinitionProvider,
                           GTWGameState gameState, LocTerm.Factory localizedStringFactory, GTWGameStats gtwGameStats)
        {
            _player = player;
            _doinklerWorldDefinitionProvider = doinklerWorldDefinitionProvider;

            _gtwGameState = gameState;
            _localizedStringFactory = localizedStringFactory;
            _gtwGameStats = gtwGameStats;
        }

        protected override void Awake()
        {
            base.Awake();
        }

        protected override IEnumerator Start()
        {
            _fileNameHeader.color = _speedrunSettings.GetUninitializedTimerTextColor();
            _currentCheckpointProgress.color = _speedrunSettings.GetUninitializedTimerTextColor();
            _chapterName.color = _speedrunSettings.GetUninitializedTimerTextColor();

            yield return base.Start();

            _mainTimer.color = _speedrunSettings.GetUninitializedTimerTextColor();

            _fileNameHeader.color = _speedrunSettings.GetNormalTimerTextColor();
            _currentCheckpointProgress.color = _speedrunSettings.GetNormalTimerTextColor();
            _chapterName.color = _speedrunSettings.GetNormalTimerTextColor();
            int currentLevel = _gameProgress.GetCurrentGameProgressLevel();

            // As of now the design looks like there's no name displayed for the D special
            //LocTerm name = _gameProgress.GetGameProgressLevelName(currentLevel);
            //name.LocalizeInto(_fileNameHeader);
            _fileNameHeader.gameObject.SetActive(false);
        }

        protected override void Update()
        {
            base.Update();

            int currentLevel = _gameProgress.GetCurrentGameProgressLevel();

            // For initialization issues
            if (currentLevel == -1)
                return;

            UpdateTotalWorldTime();
            UpdateCurrentLevel();

            if (!_init)
            {
                // Bootleg injection so the provider works for now.
                CheckpointManager cpm = GameObject.FindAnyObjectByType<CheckpointManager>();
                if (cpm != null)
                {
                    _checkpointManager = cpm;
                    _maxCheckpoint = _checkpointManager.CheckpointData.levelCheckpointDatas.OrderBy(x => x.checkpointId).Last().checkpointId;
                    _init = true;
                }
            }
        }

        private void OnEnable()
        {
            RegisterEvents();
        }

        private void RegisterEvents()
        {
            Events.Subscribe(GTWEvents.DOINKLER_STAGE_COMPLETE, Events_OnStageCompleted);
            Events.Subscribe(GTWEvents.DOINKLER_STAGE_START, Events_OnStageStarted);
        }

        private void Events_OnStageStarted()
        {
            _isTimeOverride = false;
            UpdateChapterNameLocalization();
        }

        private void Events_OnStageCompleted()
        {
            _isTimeOverride = true;
        }

        private void OnDisable()
        {
            UnregisterEvents();
        }

        private void UnregisterEvents()
        {
            Events.UnSubscribe(GTWEvents.DOINKLER_STAGE_COMPLETE, Events_OnStageCompleted);
            Events.UnSubscribe(GTWEvents.DOINKLER_STAGE_START, Events_OnStageStarted);
        }

        /*
        protected override void UpdateChapterNameLocalization()
        {
            _currentLevelNameLocalized = "";

            if (!_gtwGameState.GameLevelDefinition)
            {
                // TODO - JP - GTW-747: This occurs when we are in Unity editor and starting from the doinkler level itself and not going through the titlescene flow.
                _currentLevelNameLocalized = _localizedStringFactory.Create(LocTerm.LocalizationType.NonLocalized, "ERROR: No Game Level Definition");
                return;
            }

            if (!string.IsNullOrEmpty(_gtwGameState.GameLevelDefinition.LevelName.mTerm))
            {
                _currentLevelNameLocalized = _localizedStringFactory.Create(LocTerm.LocalizationType.Localized, _gtwGameState.GameLevelDefinition.LevelName);
            }
            else
            {
                _currentLevelNameLocalized = _localizedStringFactory.Create(LocTerm.LocalizationType.NonLocalized, _gtwGameState.GameLevelDefinition.fallbackLevelName);
            }

        }*/

        private void UpdateTotalWorldTime()
        {
            if (!_doinklerWorldDefinitionProvider.Current)
                return;

            float totalTime = 0f;
            foreach (GTWGameLevelDefinition gtwGameLevelDefinition in _doinklerWorldDefinitionProvider.Current.GameLevels)
            {
                totalTime += PlayerPrefs.GetFloat(gtwGameLevelDefinition.UniqueIDTotal);
            }
            int currentLevel = _gameProgress.GetCurrentGameProgressLevel();


            TimeSpan totalTimeSpan = TimeSpan.FromSeconds(GetIndividualSplit(currentLevel));
            _mainTimer.text = UnityUtils.ConvertTimespanToTotalHoursMinutesSeconds(totalTimeSpan);
        }

        private void UpdateCurrentLevel()
        {
            int currentLevel = _gameProgress.GetCurrentGameProgressLevel();

            //float currentTimeInLevel = _gameProgress.GetGameSecondsElapsedInLevel(currentLevel);
            //TimeSpan currentTime = TimeSpan.FromMilliseconds(currentTimeInLevel * 1000f);
            //_currentLevelTimer.text = GTWUtils.GetFormattedTimeOrDefaultString(currentTime.TotalSeconds.ToString());

            // in this mode we're looking to summarize chapter progress instead of showing timer so I'm trying this hack
            _currentCheckpointProgress.text = $"Checkpoint: {currentLevel}/{_maxCheckpoint}";

            int currentHeightPercent = 0;
            // TODO: hook up to real height from stats provider?
            //currentHeightPercent = Mathf.RoundToInt(_gtwGameStats.TotalHeightGained / 100f);
            if (_checkpointManager != null)
            {
                CheckpointData.LevelCheckpointData starting = _checkpointManager.StartingCheckpoint;
                CheckpointData.LevelCheckpointData final = _checkpointManager.CheckpointData.levelCheckpointDatas.First(x => x.checkpointId == _maxCheckpoint);
                float progress = _player.GetPlayerPosition().y - starting.position.y;
                float max = final.position.y - starting.position.y;
                currentHeightPercent = Mathf.RoundToInt((progress / max) * 100f);
            }
            _currentHeightProgress.text = $"Height: {currentHeightPercent}%";
        }

        protected override void SetupSegmentTimer(SpeedRunSingleTime timer, int chapter)
        {
            timer.SetColorBG((chapter % 2) == 0 ? bgEven : bgOdd);
            timer.ActivateImage();

            LocTerm name = _gameProgress.GetGameProgressLevelName(chapter);
            timer.SetChapterName(name);

            bool targetTimeExists = IsTargetTimeAvailableForChapter(chapter);
            if (_displaySplitsTargets && targetTimeExists)
            {
                TimeSpan chapterTarget = TimeSpan.FromSeconds(_targetTimes[chapter]);
                timer.SetTimer(UnityUtils.ConvertTimespanToTotalHoursMinutesSeconds(chapterTarget)); // always use same format so they look even
            }
            else
            {
                timer.SetTimer(_speedrunSettings.GetDefaultTimerText());
            }

            SpeedrunSettings.SpeedrunTimerConfig config = _speedrunSettings.GetPlayerSelectedTimerConfig();
            if (config.showPersonalBestDeltas)
            {
                float chapterTime = GetIndividualSplit(chapter);
                bool timeDataExists = !chapterTime.Approx(0f);
                if (targetTimeExists && timeDataExists)
                {
                    // If we're loading a saved game the time data already exists
                    float chapterSplitSeconds = GetIndividualSplit(chapter);
                    _deltasAreCumulative = config.cumulativePersonalBestDeltas;
                    AddDeltaTimeToSegmentTimer(chapterSplitSeconds, chapter, _deltasAreCumulative);
                }
                else
                {
                    // we want the timer to exist in the UI and reserve space for its info, but be invisible
                    // until the player actually finishes that chapter
                    timer.SetDiffTimer("");
                }
            }
            else
            {
                timer.HideDiffTimer();
            }
        }

        protected override void AddDeltaTimeToSegmentTimer(float segmentTime, int segmentIndex, bool cumulativeDeltas = false)
        {
            base.AddDeltaTimeToSegmentTimer(segmentTime, segmentIndex, cumulativeDeltas);
            SpeedrunSettings.PBStyleSettings pbsettings = _speedrunSettings.GetPersonalBestsStyleSettings();
            Color c = pbsettings.passedTarget;
            /*
            GTWGameWorldDefinition currentWorldDefinition = _doinklerSpecialDefinition;
            if(!currentWorldDefinition.IsWorldCompleted)
            {
                _segmentTimes[segmentIndex].SetDiffTimer("Incomplete"); // TODO: JP - Add localization here
                _segmentTimes[segmentIndex].SetDiffColor(c);
            }*/
        }

        protected override float GetIndividualSplit(int progressLevel)
        {
            /*
            if (progressLevel >= 1)
            {
                // TODO - JP - GTW-747: This occurs when we are in Unity editor and starting from the doinkler level itself and not going through the titlescene flow.
                // FG: copied over from porfolio timer logic, not sure if it still applies for the special.
                Debug.LogError($"DoinklerSpecialSpeedrunTimer: Attempting to get time for world {progressLevel} but only have {1} world.");
                return 0f;
            }*/

            float totalTime = 0f;

            totalTime += PlayerPrefs.GetFloat(_doinklerSpecialDefinition.UniqueIDTotal);

            if (!_isTimeOverride)
            {
                totalTime += _gameProgress.GetGameSecondsElapsedInLevel(progressLevel);
            }

            return totalTime;
        }

        protected override void SaveRecordTimes()
        {
            SpeedrunTimerData data = new SpeedrunTimerData();
            int maxLevel = _gameProgress.GetMaxGameProgressLevel();
            data.times = new float[maxLevel];
            /*
            for (int i = 0; i < maxLevel; i++)
            {
                GTWGameWorldDefinition currentWorldDefinition = _doinklerSpecialDefinition;
                if (!currentWorldDefinition.IsWorldCompleted)
                {
                    continue;
                }
                float timeTotal = 0f;
                foreach (GTWGameLevelDefinition gtwGameLevelDefinition in currentWorldDefinition.GameLevels)
                {
                    timeTotal += PlayerPrefs.GetFloat(gtwGameLevelDefinition.UniqueIDTotal);
                }
                data.times[i] = timeTotal;
            }*/

            switch (_speedrunSettings.GetPersonalBestSaveLocation())
            {
                case SpeedrunSettings.SpeedrunPersonalBestsSaveLocationEnum.DoNotSave:
                    Debug.LogWarning($"Best splits currently configured not to be saved.");
                    break;
                case SpeedrunSettings.SpeedrunPersonalBestsSaveLocationEnum.SaveSlot:
                    SaveRecordToSlot(data);
                    break;
                case SpeedrunSettings.SpeedrunPersonalBestsSaveLocationEnum.LocalLow:
                    SaveRecordToLocalLow(data);
                    break;
                default:
                    Debug.LogError($"Best splits save location has unsupported configuration {_speedrunSettings.GetPersonalBestSaveLocation()}");
                    break;
            }
        }
    }
}